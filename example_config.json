{"database": {"host": "localhost", "port": 1433, "database": "wind_db", "username": "your_username", "password": "your_password"}, "backtest": {"start_date": "2020-01-01", "end_date": "2023-12-31", "index_code": "000906.SH", "initial_capital": 1000000, "commission_rate": 0.0003, "slippage_rate": 0.001}, "macro_factors": {"production": {"enabled": true, "pmi_window": 12, "lookback_months": 3}, "consumption": {"enabled": true, "cpi_weight": 0.5, "ppi_weight": 0.5, "lookback_months": 3}, "investment": {"enabled": true, "lookback_months": 12}, "monetary": {"enabled": true, "lookback_days": 90}, "exchange_rate": {"enabled": true, "lookback_months": 3}, "bond": {"enabled": true, "lookback_months": 3}, "credit": {"enabled": false, "lookback_months": 3}}, "meso_factors": {"margin_trading": {"enabled": true, "short_window": 120, "long_window": 240}, "turnover_trend": {"enabled": true, "ma_short": 120, "ma_long": 240, "max_min_windows": [10, 30, 60]}, "market_money_flow": {"enabled": true, "short_window": 120, "long_window": 240}, "main_money_flow": {"enabled": true, "short_window": 120, "long_window": 240}, "vix": {"enabled": true, "lookback_months": 3}}, "micro_factors": {"pb_ps": {"enabled": true, "zscore_clip": 1.5}, "momentum": {"enabled": true, "periods": [63, 126, 252], "zscore_clip": 1.5}, "roe": {"enabled": true, "zscore_clip": 1.5}, "liquidity": {"enabled": true, "periods": [63, 126, 252], "zscore_clip": 1.5}}, "technical_factors": {"bias": {"enabled": true, "period": 26, "threshold": 5}, "aroon": {"enabled": false, "period": 14, "threshold": 70}, "adx": {"enabled": false, "period": 14}, "uo": {"enabled": false, "period1": 7, "period2": 14, "period3": 28, "buy_threshold": 70, "sell_threshold": 50}, "atr": {"enabled": false, "period": 14, "multiplier": 2}, "udvd": {"enabled": false, "period": 20}, "new_high_low": {"enabled": false, "lookback_days": 252, "smooth_window": 20}}, "factor_weights": {"macro_score": 0.4, "meso_score": 0.3, "micro_score": 0.3}, "signal": {"signal_threshold": 0, "position_base": 0.5, "position_multiplier": 0.5}, "output": {"save_charts": true, "chart_dir": "./charts/", "save_results": true, "result_dir": "./results/", "file_prefix": "csi800_timing_custom"}}