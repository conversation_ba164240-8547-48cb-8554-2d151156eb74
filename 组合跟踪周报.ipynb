{"cells": [{"cell_type": "code", "execution_count": null, "id": "e5f87bc2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pymysql\n", "import datetime\n", "\n", "config = {\n", "    'host': '*************',\n", "    'port': 3306,\n", "    'user': 'ct_wind_user',\n", "    'password': 'Ctjg2025',\n", "    'database': 'windsh'\n", "}\n", "db = pymysql.connect(**config)\n", "\n", "def sql_to_df(db, sql_query):\n", "    cursor=db.cursor()\n", "    cursor.execute(sql_query)\n", "    info = cursor.fetchall()\n", "    cols = cursor.description\n", "    col = []\n", "    for i in cols:\n", "        col.append(i[0])\n", "    data = pd.DataFrame(info,columns=col)\n", "    return data"]}, {"cell_type": "code", "execution_count": 2, "id": "ad69f4f6", "metadata": {}, "outputs": [], "source": ["sql = '''\n", "select trade_dt as date, \n", "s_marsum_exchmarket as exchange,\n", "s_marsum_tradingbalance as financing_balance, \n", "s_marsum_seclendingbalance as securities_lending_balance \n", "from asharemargintradesum\n", "'''\n", "margin_trade = sql_to_df(db, sql).groupby('date').agg(\n", "    {'financing_balance': 'sum', \n", "     'securities_lending_balance': 'sum'}\n", ").sort_values('date').reset_index()"]}, {"cell_type": "code", "execution_count": 3, "id": "f2f3fa60", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "financing_balance", "rawType": "object", "type": "unknown"}, {"name": "securities_lending_balance", "rawType": "object", "type": "unknown"}], "ref": "ec5e75f4-a0b6-4a63-8586-4991c1532aad", "rows": [["0", "20250704", "1840046628641.0000", "12880116479.0000"], ["1", "20250707", "1846405684077.0000", "12974502637.0000"], ["2", "20250708", "1851893440555.0000", "13037899494.0000"]], "shape": {"columns": 3, "rows": 3}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>financing_balance</th>\n", "      <th>securities_lending_balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20250704</td>\n", "      <td>1840046628641.0000</td>\n", "      <td>12880116479.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20250707</td>\n", "      <td>1846405684077.0000</td>\n", "      <td>12974502637.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20250708</td>\n", "      <td>1851893440555.0000</td>\n", "      <td>13037899494.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       date   financing_balance securities_lending_balance\n", "0  20250704  1840046628641.0000           12880116479.0000\n", "1  20250707  1846405684077.0000           12974502637.0000\n", "2  20250708  1851893440555.0000           13037899494.0000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["margin_trade"]}, {"cell_type": "code", "execution_count": 26, "id": "8e1c8bd2", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "exchange", "rawType": "object", "type": "string"}, {"name": "financing_balance", "rawType": "object", "type": "unknown"}, {"name": "securities_lending_balance", "rawType": "object", "type": "unknown"}], "ref": "f43fab62-23c9-441e-ba15-ab211653514e", "rows": [["820", "20220104", "SSE", "906057651977.0000", "73986984743.0000"], ["1496", "20220104", "SZSE", "812970787875.0000", "41860637037.0000"], ["773", "20220105", "SSE", "904167215798.0000", "72172661033.0000"], ["245", "20220105", "SZSE", "812154757945.0000", "41043672878.0000"], ["126", "20220106", "SSE", "904082746979.0000", "72154331847.0000"], ["1305", "20220106", "SZSE", "813766691068.0000", "40952654457.0000"], ["1894", "20220107", "SSE", "895813442330.0000", "71268730282.0000"], ["627", "20220107", "SZSE", "806888273850.0000", "40628967106.0000"], ["2069", "20220110", "SZSE", "807663576548.0000", "40519373410.0000"], ["1830", "20220110", "SSE", "896873723408.0000", "71151294063.0000"], ["1293", "20220111", "SZSE", "806866742672.0000", "40175931968.0000"], ["1755", "20220111", "SSE", "896737126432.0000", "70433794659.0000"], ["29", "20220112", "SSE", "898405575020.0000", "70938053988.0000"], ["342", "20220112", "SZSE", "807385955517.0000", "40413672360.0000"], ["1194", "20220113", "SZSE", "808031003026.0000", "39766362781.0000"], ["514", "20220113", "SSE", "897215444391.0000", "69762671602.0000"], ["2209", "20220114", "SSE", "895539506386.0000", "69193804514.0000"], ["4", "20220114", "SZSE", "805386702004.0000", "39854366728.0000"], ["842", "20220117", "SZSE", "808598834359.0000", "40288357880.0000"], ["1814", "20220117", "SSE", "896735334253.0000", "69711053098.0000"], ["728", "20220118", "SZSE", "806827639701.0000", "40432195764.0000"], ["1090", "20220118", "SSE", "895190092705.0000", "69781414468.0000"], ["373", "20220119", "SSE", "892008945432.0000", "68403399190.0000"], ["1657", "20220119", "SZSE", "806760712605.0000", "39383641709.0000"], ["1372", "20220120", "SZSE", "801786415773.0000", "39462223220.0000"], ["1682", "20220120", "SSE", "886686826861.0000", "67834549214.0000"], ["1139", "20220121", "SSE", "881353735363.0000", "66795846737.0000"], ["1489", "20220121", "SZSE", "795148628403.0000", "38580120581.0000"], ["2040", "20220124", "SSE", "879433636370.0000", "65372745895.0000"], ["959", "20220124", "SZSE", "794566460854.0000", "37782718960.0000"], ["839", "20220125", "SSE", "875761757447.0000", "62815257517.0000"], ["1055", "20220125", "SZSE", "789093034954.0000", "36677373905.0000"], ["2", "20220126", "SZSE", "784865179078.0000", "36446325550.0000"], ["2006", "20220126", "SSE", "872693075234.0000", "63184486286.0000"], ["1450", "20220127", "SZSE", "780154059779.0000", "34794206744.0000"], ["727", "20220127", "SSE", "868250393955.0000", "60473114561.0000"], ["961", "20220128", "SZSE", "765544431852.0000", "33751026685.0000"], ["1791", "20220128", "SSE", "854740314467.0000", "59174427770.0000"], ["22", "20220207", "SZSE", "767889837723.0000", "33966495725.0000"], ["1620", "20220207", "SSE", "855901800819.0000", "60215503155.0000"], ["1651", "20220208", "SSE", "854484927188.0000", "61059264042.0000"], ["2127", "20220208", "SZSE", "767991506876.0000", "34072387097.0000"], ["273", "20220209", "SZSE", "770261585017.0000", "34937932288.0000"], ["1177", "20220209", "SSE", "854850368933.0000", "62437858058.0000"], ["1023", "20220210", "SSE", "853570690387.0000", "62421930804.0000"], ["439", "20220210", "SZSE", "770587356687.0000", "34771623339.0000"], ["1303", "20220211", "SSE", "849788559074.0000", "61938605900.0000"], ["209", "20220211", "SZSE", "768161542397.0000", "34164312819.0000"], ["1459", "20220214", "SSE", "852506500811.0000", "60968329760.0000"], ["680", "20220214", "SZSE", "768460557245.0000", "33960084700.0000"]], "shape": {"columns": 4, "rows": 2272}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>exchange</th>\n", "      <th>financing_balance</th>\n", "      <th>securities_lending_balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>820</th>\n", "      <td>20220104</td>\n", "      <td>SSE</td>\n", "      <td>906057651977.0000</td>\n", "      <td>73986984743.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1496</th>\n", "      <td>20220104</td>\n", "      <td>SZSE</td>\n", "      <td>812970787875.0000</td>\n", "      <td>41860637037.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>773</th>\n", "      <td>20220105</td>\n", "      <td>SSE</td>\n", "      <td>904167215798.0000</td>\n", "      <td>72172661033.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>245</th>\n", "      <td>20220105</td>\n", "      <td>SZSE</td>\n", "      <td>812154757945.0000</td>\n", "      <td>41043672878.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>20220106</td>\n", "      <td>SSE</td>\n", "      <td>904082746979.0000</td>\n", "      <td>72154331847.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1950</th>\n", "      <td>20250702</td>\n", "      <td>SSE</td>\n", "      <td>928080643653.0000</td>\n", "      <td>8865272383.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1281</th>\n", "      <td>20250702</td>\n", "      <td>BSE</td>\n", "      <td>5900684776.0000</td>\n", "      <td>823368.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1311</th>\n", "      <td>20250703</td>\n", "      <td>SZSE</td>\n", "      <td>911580254700.0000</td>\n", "      <td>3779541299.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2067</th>\n", "      <td>20250703</td>\n", "      <td>SSE</td>\n", "      <td>928887433388.0000</td>\n", "      <td>8963008315.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>540</th>\n", "      <td>20250703</td>\n", "      <td>BSE</td>\n", "      <td>5919626380.0000</td>\n", "      <td>875129.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2272 rows × 4 columns</p>\n", "</div>"], "text/plain": ["          date exchange  financing_balance securities_lending_balance\n", "820   20220104      SSE  906057651977.0000           73986984743.0000\n", "1496  20220104     SZSE  812970787875.0000           41860637037.0000\n", "773   20220105      SSE  904167215798.0000           72172661033.0000\n", "245   20220105     SZSE  812154757945.0000           41043672878.0000\n", "126   20220106      SSE  904082746979.0000           72154331847.0000\n", "...        ...      ...                ...                        ...\n", "1950  20250702      SSE  928080643653.0000            8865272383.0000\n", "1281  20250702      BSE    5900684776.0000                823368.0000\n", "1311  20250703     SZSE  911580254700.0000            3779541299.0000\n", "2067  20250703      SSE  928887433388.0000            8963008315.0000\n", "540   20250703      BSE    5919626380.0000                875129.0000\n", "\n", "[2272 rows x 4 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_to_df(db, sql).sort_values('date')"]}, {"cell_type": "code", "execution_count": 24, "id": "5ff12a5e", "metadata": {}, "outputs": [], "source": ["margin_trade.to_csv('margin_trade.csv', encoding='gbk', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "9dc796f7", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "code", "rawType": "object", "type": "string"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "close", "rawType": "object", "type": "unknown"}], "ref": "91e3dcdd-329b-4c83-9904-91771113d4ac", "rows": [["0", "801770.SI", "20250627", "3197.0100"], ["1", "851786.SI", "20250627", "3401.1200"], ["2", "859622.SI", "20250627", "2599.3900"], ["3", "851614.SI", "20250627", "1826.8400"], ["4", "801720.SI", "20250627", "1923.8600"], ["5", "801766.SI", "20250627", "1303.0800"], ["6", "851112.SI", "20250627", "38956.8500"], ["7", "851452.SI", "20250627", "1511.9500"], ["8", "850322.SI", "20250627", "1233.6100"], ["9", "850325.SI", "20250627", "5741.3400"], ["10", "801037.SI", "20250627", "4520.3200"], ["11", "801250.SI", "20250627", "4675.0900"], ["12", "851281.SI", "20250627", "1912.6000"], ["13", "851782.SI", "20250627", "3153.0400"], ["14", "850924.SI", "20250627", "4151.0000"], ["15", "801183.SI", "20250627", "944.5800"], ["16", "803614.SI", "20250627", "11218.4580"], ["17", "851438.SI", "20250627", "1737.3100"], ["18", "806170.SI", "20250627", "207.2100"], ["19", "801736.SI", "20250627", "1924.5600"], ["20", "851412.SI", "20250627", "3462.2900"], ["21", "850333.SI", "20250627", "3372.1700"], ["22", "801018.SI", "20250627", "4136.1200"], ["23", "857431.SI", "20250627", "17927.4500"], ["24", "801273.SI", "20250627", "701.0600"], ["25", "851532.SI", "20250627", "9615.2000"], ["26", "851141.SI", "20250627", "15393.2400"], ["27", "857821.SI", "20250627", "1901.1100"], ["28", "801750.SI", "20250627", "4746.8800"], ["29", "801110.SI", "20250627", "8320.9900"], ["30", "859711.SI", "20250627", "2650.9400"], ["31", "801740.SI", "20250627", "1596.8200"], ["32", "851325.SI", "20250627", "785.5200"], ["33", "801843.SI", "20250627", "13243.0100"], ["34", "806090.SI", "20250627", "237.7200"], ["35", "851617.SI", "20250627", "1315.2200"], ["36", "850784.SI", "20250627", "3222.4700"], ["37", "857382.SI", "20250627", "2182.2600"], ["38", "801780.SI", "20250627", "4436.6500"], ["39", "851941.SI", "20250627", "1280.8000"], ["40", "857851.SI", "20250627", "869.7000"], ["41", "801813.SI", "20250627", "4547.7600"], ["42", "851711.SI", "20250627", "3977.8600"], ["43", "850922.SI", "20250627", "11023.3700"], ["44", "801276.SI", "20250627", "1029.8900"], ["45", "850381.SI", "20250627", "4335.8200"], ["46", "801204.SI", "20250627", "5117.8600"], ["47", "806140.SI", "20250627", "348.3900"], ["48", "801950.SI", "20250627", "2539.5500"], ["49", "858812.SI", "20250627", "3706.1700"]], "shape": {"columns": 3, "rows": 2836}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>close</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>801770.SI</td>\n", "      <td>20250627</td>\n", "      <td>3197.0100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>851786.SI</td>\n", "      <td>20250627</td>\n", "      <td>3401.1200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>859622.SI</td>\n", "      <td>20250627</td>\n", "      <td>2599.3900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>851614.SI</td>\n", "      <td>20250627</td>\n", "      <td>1826.8400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>801720.SI</td>\n", "      <td>20250627</td>\n", "      <td>1923.8600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2831</th>\n", "      <td>801832.SI</td>\n", "      <td>20250704</td>\n", "      <td>3773.4300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2832</th>\n", "      <td>801863.SI</td>\n", "      <td>20250704</td>\n", "      <td>2290.8400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2833</th>\n", "      <td>850232.SI</td>\n", "      <td>20250704</td>\n", "      <td>506.3100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2834</th>\n", "      <td>852031.SI</td>\n", "      <td>20250704</td>\n", "      <td>2629.6300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2835</th>\n", "      <td>852021.SI</td>\n", "      <td>20250704</td>\n", "      <td>4006.7600</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2836 rows × 3 columns</p>\n", "</div>"], "text/plain": ["           code      date      close\n", "0     801770.SI  20250627  3197.0100\n", "1     851786.SI  20250627  3401.1200\n", "2     859622.SI  20250627  2599.3900\n", "3     851614.SI  20250627  1826.8400\n", "4     801720.SI  20250627  1923.8600\n", "...         ...       ...        ...\n", "2831  801832.SI  20250704  3773.4300\n", "2832  801863.SI  20250704  2290.8400\n", "2833  850232.SI  20250704   506.3100\n", "2834  852031.SI  20250704  2629.6300\n", "2835  852021.SI  20250704  4006.7600\n", "\n", "[2836 rows x 3 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 申万行业指数上周收益率\n", "\n", "sql = '''\n", "select S_INFO_WINDCODE as code, TRADE_DT as date, S_DQ_CLOSE as close from aswsindexeod\n", "where TRADE_DT >= '20250627'\n", "'''\n", "sw_index = sql_to_df(db, sql).sort_values('date').reset_index(drop=True)\n", "sw_index"]}, {"cell_type": "code", "execution_count": 13, "id": "6da773aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['801770.SI', '851786.SI', '859622.SI', '851614.SI', '801720.SI',\n", "       '801766.SI', '851112.SI', '851452.SI', '850322.SI', '850325.SI',\n", "       '801037.SI', '801250.SI', '851281.SI', '851782.SI', '850924.SI',\n", "       '801183.SI', '803614.SI', '851438.SI', '806170.SI', '801736.SI',\n", "       '851412.SI', '850333.SI', '801018.SI', '857431.SI', '801273.SI',\n", "       '851532.SI', '851141.SI', '857821.SI', '801750.SI', '801110.SI',\n", "       '859711.SI', '801740.SI', '851325.SI', '801843.SI', '806090.SI',\n", "       '851617.SI', '850784.SI', '857382.SI', '801780.SI', '851941.SI',\n", "       '857851.SI', '801813.SI', '851711.SI', '850922.SI', '801276.SI',\n", "       '850381.SI', '801204.SI', '806140.SI', '801950.SI', '858812.SI',\n", "       '801841.SI', '801960.SI', '859721.SI', '801145.SI', '801270.SI',\n", "       '801142.SI', '850935.SI', '801963.SI', '801055.SI', '852033.SI',\n", "       '857334.SI', '859633.SI', '801056.SI', '851042.SI', '850812.SI',\n", "       '857371.SI', '850703.SI', '850926.SI', '850616.SI', '801080.SI',\n", "       '850326.SI', '801153.SI', '801712.SI', '801823.SI', '859512.SI',\n", "       '857641.SI', '801745.SI', '850362.SI', '806050.SI', '851151.SI',\n", "       '857841.SI', '801767.SI', '850854.SI', '801737.SI', '801971.SI',\n", "       '801151.SI', '801014.SI', '859852.SI', '850782.SI', '801156.SI',\n", "       '801120.SI', '801074.SI', '850853.SI', '850181.SI', '801952.SI',\n", "       '806070.SI', '857692.SI', '801051.SI', '801863.SI', '850442.SI',\n", "       '801783.SI', '851741.SI', '852311.SI', '850781.SI', '801126.SI',\n", "       '851247.SI', '801726.SI', '801970.SI', '859632.SI', '801962.SI',\n", "       '851312.SI', '801054.SI', '857251.SI', '851511.SI', '851331.SI',\n", "       '801116.SI', '850741.SI', '850332.SI', '857373.SI', '850136.SI',\n", "       '801992.SI', '857121.SI', '850727.SI', '857122.SI', '852214.SI',\n", "       '801102.SI', '851315.SI', '806001.SI', '850822.SI', '801038.SI',\n", "       '801993.SI', '801741.SI', '801210.SI', '857112.SI', '801713.SI',\n", "       '857831.SI', '806200.SI', '806180.SI', '851437.SI', '801003.SI',\n", "       '801083.SI', '857451.SI', '801179.SI', '850726.SI', '851563.SI',\n", "       '850751.SI', '801045.SI', '851512.SI', '801093.SI', '851025.SI',\n", "       '801982.SI', '851282.SI', '801127.SI', '801744.SI', '850331.SI',\n", "       '806060.SI', '850363.SI', '801724.SI', '801082.SI', '851612.SI',\n", "       '801972.SI', '801721.SI', '850552.SI', '851543.SI', '806110.SI',\n", "       '801030.SI', '801053.SI', '801092.SI', '850135.SI', '850354.SI',\n", "       '852112.SI', '850771.SI', '801811.SI', '801010.SI', '851423.SI',\n", "       '857344.SI', '857243.SI', '801951.SI', '801738.SI', '801143.SI',\n", "       '801180.SI', '801072.SI', '852021.SI', '850339.SI', '851424.SI',\n", "       '801040.SI', '851122.SI', '859631.SI', '850523.SI', '850151.SI',\n", "       '801202.SI', '852226.SI', '857221.SI', '801203.SI', '850952.SI',\n", "       '801128.SI', '801152.SI', '801112.SI', '859811.SI', '801193.SI',\n", "       '850551.SI', '801832.SI', '851931.SI', '851784.SI', '851631.SI',\n", "       '850111.SI', '801790.SI', '851787.SI', '850925.SI', '857691.SI',\n", "       '851927.SI', '801765.SI', '801103.SI', '801044.SI', '852213.SI',\n", "       '851783.SI', '859712.SI', '859521.SI', '801735.SI', '851811.SI',\n", "       '801616.SI', '801980.SI', '850841.SI', '801821.SI', '851522.SI',\n", "       '801095.SI', '850772.SI', '801078.SI', '806080.SI', '850818.SI',\n", "       '801005.SI', '850521.SI', '851523.SI', '801274.SI', '801231.SI',\n", "       '857411.SI', '801730.SI', '850623.SI', '801016.SI', '850553.SI',\n", "       '852062.SI', '801178.SI', '801129.SI', '859714.SI', '801114.SI',\n", "       '801831.SI', '859821.SI', '850725.SI', '850716.SI', '801076.SI',\n", "       '851731.SI', '859822.SI', '806040.SI', '851813.SI', '851533.SI',\n", "       '801981.SI', '801017.SI', '806010.SI', '850382.SI', '852063.SI',\n", "       '851041.SI', '851812.SI', '851436.SI', '850341.SI', '801084.SI',\n", "       '801086.SI', '852032.SI', '801050.SI', '850531.SI', '802601.SI',\n", "       '851131.SI', '859511.SI', '850233.SI', '852121.SI', '801638.SI',\n", "       '801782.SI', '801853.SI', '857381.SI', '851242.SI', '850351.SI',\n", "       '801155.SI', '857323.SI', '850173.SI', '801085.SI', '801015.SI',\n", "       '801039.SI', '801995.SI', '801742.SI', '852034.SI', '801194.SI',\n", "       '801733.SI', '801223.SI', '801784.SI', '850814.SI', '850715.SI',\n", "       '851771.SI', '851534.SI', '857321.SI', '801081.SI', '857111.SI',\n", "       '801881.SI', '850152.SI', '852111.SI', '851425.SI', '851326.SI',\n", "       '801812.SI', '801743.SI', '806160.SI', '850913.SI', '801615.SI',\n", "       '857372.SI', '801300.SI', '850335.SI', '801124.SI', '801036.SI',\n", "       '801161.SI', '801141.SI', '801096.SI', '857421.SI', '852183.SI',\n", "       '851922.SI', '801115.SI', '850831.SI', '852041.SI', '851524.SI',\n", "       '851251.SI', '801723.SI', '851610.SI', '850154.SI', '801132.SI',\n", "       '851314.SI', '801101.SI', '850912.SI', '801260.SI', '801113.SI',\n", "       '850122.SI', '851831.SI', '850232.SI', '850713.SI', '850372.SI',\n", "       '801230.SI', '850355.SI', '801275.SI', '857355.SI', '851161.SI',\n", "       '801154.SI', '851232.SI', '801994.SI', '851116.SI', '850861.SI',\n", "       '801033.SI', '850783.SI', '850615.SI', '851246.SI', '850702.SI',\n", "       '801637.SI', '850412.SI', '806100.SI', '801731.SI', '851721.SI',\n", "       '857375.SI', '850614.SI', '801206.SI', '801012.SI', '801001.SI',\n", "       '850172.SI', '801890.SI', '857336.SI', '850813.SI', '851233.SI',\n", "       '801133.SI', '851026.SI', '850711.SI', '850338.SI', '801219.SI',\n", "       '801722.SI', '801764.SI', '850923.SI', '850817.SI', '858811.SI',\n", "       '801639.SI', '801007.SI', '801880.SI', '857354.SI', '852182.SI',\n", "       '857244.SI', '857651.SI', '851616.SI', '801760.SI', '801280.SI',\n", "       '850936.SI', '806130.SI', '857362.SI', '857261.SI', '850343.SI',\n", "       '801131.SI', '851027.SI', '851785.SI', '801111.SI', '851271.SI',\n", "       '857242.SI', '852031.SI', '851521.SI', '851542.SI', '851491.SI',\n", "       '801271.SI', '801991.SI', '801272.SI', '850832.SI', '857236.SI',\n", "       '851413.SI', '801851.SI', '851241.SI', '801822.SI', '801104.SI',\n", "       '801043.SI', '859621.SI', '801200.SI', '801034.SI', '801785.SI',\n", "       '850142.SI', '850544.SI', '857241.SI', '801160.SI', '850728.SI',\n", "       '859713.SI', '801130.SI', '806020.SI', '851422.SI', '806030.SI',\n", "       '850522.SI', '801711.SI', '801842.SI', '801077.SI', '859951.SI',\n", "       '850323.SI', '851243.SI', '801769.SI', '801218.SI', '850731.SI',\n", "       '850721.SI', '801710.SI', '801032.SI', '850823.SI', '801181.SI',\n", "       '851024.SI', '806120.SI', '801833.SI', '850353.SI', '851611.SI',\n", "       '801125.SI', '857674.SI', '806150.SI', '801002.SI', '801140.SI',\n", "       '801852.SI', '851761.SI', '850324.SI', '851439.SI', '801163.SI',\n", "       '850815.SI', '801191.SI', '801150.SI', '850337.SI', '850113.SI',\n", "       '801170.SI', '851329.SI', '857661.SI', '850833.SI', '852131.SI',\n", "       '857352.SI', '851316.SI', '851564.SI'], dtype=object)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["sw_index['code'].unique()"]}, {"cell_type": "code", "execution_count": 19, "id": "56145782", "metadata": {}, "outputs": [], "source": ["sw_index.to_csv('sw_index.csv', encoding='gbk')"]}, {"cell_type": "code", "execution_count": 18, "id": "ee3c3bb5", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['20250627', '20250630', '20250701', '20250702', '20250703',\n", "       '20250704'], dtype=object)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["sw_index['date'].unique()"]}, {"cell_type": "code", "execution_count": 26, "id": "e291f85c", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Unnamed: 0", "rawType": "int64", "type": "integer"}, {"name": "OBJECT_ID", "rawType": "object", "type": "string"}, {"name": "S_INFO_WINDCODE", "rawType": "object", "type": "string"}, {"name": "TRADE_DT", "rawType": "int64", "type": "integer"}, {"name": "CRNCY_CODE", "rawType": "object", "type": "string"}, {"name": "S_DQ_PRECLOSE", "rawType": "float64", "type": "float"}, {"name": "S_DQ_OPEN", "rawType": "float64", "type": "float"}, {"name": "S_DQ_HIGH", "rawType": "float64", "type": "float"}, {"name": "S_DQ_LOW", "rawType": "float64", "type": "float"}, {"name": "S_DQ_CLOSE", "rawType": "float64", "type": "float"}, {"name": "S_DQ_CHANGE", "rawType": "float64", "type": "float"}, {"name": "S_DQ_PCTCHANGE", "rawType": "float64", "type": "float"}, {"name": "S_DQ_VOLUME", "rawType": "float64", "type": "float"}, {"name": "S_DQ_AMOUNT", "rawType": "float64", "type": "float"}, {"name": "SEC_ID", "rawType": "object", "type": "string"}, {"name": "OPDATE", "rawType": "object", "type": "string"}, {"name": "OPMODE", "rawType": "int64", "type": "integer"}], "ref": "2577fb14-69d3-424c-8856-9cd37ce1abd1", "rows": [["0", "0", "-878160", "399107.SZ", "19951219", "CNY", "122.55", "122.135", "122.996", "121.815", "122.55", "0.0", "0.0", "999881.0", "251274.0", "2C02", "2025-02-25 17:10:13", "1"], ["1", "1", "{00000267-A236-44EE-BA58-1BFA1627023A}", "CN6046.CNI", "20231113", "CNY", "1895.9237", "1899.2997", "1905.0503", "1875.6984", "1889.9613", "-5.9624", "-0.3145", "17671669.8", "23143814.936", "S4593506", "2023-11-13 19:44:26", "0"], ["2", "2", "{000003E4-27CB-444E-8686-91352BC35BFA}", "h40001.SH", "20230418", "CNY", "3650.3086", null, null, null, "3677.629", "27.3204", "0.7484", null, null, "S4118097", "2023-04-18 17:35:56", "0"], ["3", "3", "{0000060C-C985-4069-9245-1264C9086DD3}", "h40021.SH", "20230206", "CNY", "8423.2663", null, null, null, "8361.0967", "-62.1696", "-0.7381", null, null, "S4535053", "2023-02-06 17:35:48", "0"], ["4", "4", "{00000ABC-FD3B-4CC2-8191-E7C4B8DA5C0B}", "h30461.CSI", "20230227", "CNY", "3506.044", null, null, null, "3487.2811", "-18.7629", "-0.5352", "8314459.25", "29784780.887", "S5146829", "2023-02-27 18:03:23", "0"], ["5", "5", "{00000D0C-B198-4AA5-88B0-18488D371702}", "h30205.CSI", "20230601", "CNY", "32793.7899", "32778.1924", "33678.0282", "32758.5763", "32984.3408", "190.5509", "0.5811", "3068628.12", "18638579.329", "S4532023", "2023-06-01 18:33:43", "0"], ["6", "6", "{0000127B-BCFF-4388-BEFF-A85FE8957FCA}", "399311.SZ", "20250221", "CNY", "4134.1463", "4143.0738", "4191.9808", "4130.3744", "4190.0561", "55.9098", "1.3524", "582495835.0", "1027936073.9", "S12319", "2025-02-21 15:03:15", "0"], ["7", "7", "{00001653-76FF-4C25-8FBF-0E62260E8C5F}", "h11110.CSI", "20250509", "HKD", "1958.8686", "1964.5014", "1967.3879", "1956.3077", "1967.3879", "8.5193", "0.4349", "21592023.49", "42090080.749", "S3802957", "2025-05-09 19:09:34", "1"], ["8", "8", "{000017C4-85D1-4FCF-A23E-3D0463F07387}", "h20111.CSI", "20220207", "CNY", "1626.9968", null, null, null, "1626.904", "-0.0928", "-0.0057", "4816589.3", "5141984.275", "S4493605", "2022-02-07 18:45:05", "0"], ["9", "9", "{00002330-6ECA-47E7-A033-D3796B7CF45B}", "h30202.CSI", "20240308", "CNY", "11509.459", "11535.3253", "11682.6047", "11372.7577", "11624.4901", "115.0311", "0.9994", "9131073.75", "20005311.857", "S4532015", "2024-03-08 17:44:16", "0"], ["10", "10", "{0000265E-EF67-474A-901B-B663B45AC451}", "930671.CSI", "20221013", "CNY", "2219.551", null, null, null, "2199.2001", "-20.3509", "-0.9169", "4934609.11", "3958925.873", "S5427146", "2022-10-13 17:23:18", "0"], ["11", "11", "{00002FDD-016D-4760-A20B-9D0E6335D524}", "h20106.CSI", "20250625", "HKD", "3302.5595", null, null, null, "3362.4789", "59.9194", "1.8143", "36782617.8", "45746120.261", "S4493600", "2025-06-25 18:26:18", "0"], ["12", "12", "{00003590-DD83-4BB1-9878-96B7FBC2438E}", "931069.CSI", "20240624", "CNY", "3924.6353", "3904.1354", "3944.4654", "3898.1945", "3917.693", "-6.9423", "-0.1769", "68529378.67", "91348449.135", "S9421622", "2024-06-24 17:49:24", "0"], ["13", "13", "{00003B5D-3456-435D-AA9C-432B59AF203F}", "h20875.CSI", "20220906", "CNY", "2709.0334", null, null, null, "2803.7989", "94.7655", "3.4981", null, null, "S6609513", "2022-09-06 17:15:39", "0"], ["14", "14", "{00004CDA-7CD3-40CC-8BEF-B69DF493964B}", "h30435.CSI", "20220208", "CNY", "2652.376", null, null, null, "2658.0227", "5.6467", "0.2129", "11798401.11", "12369262.604", "S5114972", "2022-02-08 18:53:59", "0"], ["15", "15", "{00004D93-0A72-4A54-90A2-91400D869044}", "930670.CSI", "20241203", "CNY", "7096.4727", null, null, null, "7065.4283", "-31.0444", "-0.4375", "1904656.76", "1428727.903", "S5427145", "2024-12-03 17:48:24", "0"], ["16", "16", "{0000550B-D222-462E-8FA9-4472E31A0B1D}", "932277CNY200.CSI", "20250513", "CNY", "869.415", null, null, null, "836.4918", "-32.9232", "-3.7868", "2420513.14", "10434514.036", "S25746386", "2025-05-13 19:15:11", "0"], ["17", "17", "{0000557E-F236-40B7-BA6F-334EBAE22E6F}", "932026HKD00.CSI", "20231113", "HKD", "1300.4133", null, null, null, "1303.1929", "2.7796", "0.2137", "28502721.36", "76845625.254", "S13523727", "2023-11-13 18:16:21", "0"], ["18", "18", "{00005970-70B7-4F91-B141-B615A7DB33AF}", "931530.CSI", "20230313", "CNY", "1881.1179", null, null, null, "1944.6243", "63.5064", "3.376", "16027290.07", "12964865.435", "S5096575", "2023-03-14 08:24:18", "0"], ["19", "19", "{00005A74-9235-47EB-8D41-B00E7011B112}", "399970.CSI", "20250314", "CNY", "3444.2617", "3437.3228", "3520.2734", "3432.5566", "3518.023", "73.7613", "2.1416", "81158439.11", "163716509.225", "S4889125", "2025-03-14 18:06:26", "1"], ["20", "20", "{00005C2D-6E87-4ABD-A409-3B1898E6B918}", "931727CNY01.CSI", "20230207", "CNY", "1542.3361", null, null, null, "1535.3971", "-6.939", "-0.4499", "1659709.77", "4610650.24", "S11935886", "2023-02-07 18:16:13", "0"], ["21", "21", "{00005D12-C02A-400E-B8A4-41B346A3F077}", "h20766.CSI", "20230509", "CNY", "1734.8589", null, null, null, "1680.8854", "-53.9735", "-3.1111", null, null, "S5646330", "2023-05-09 17:27:48", "0"], ["22", "22", "{00005D43-0029-42FA-A441-F303EFC666A3}", "h30495.CSI", "20220907", "HKD", "15282.2863", null, null, null, "15069.8322", "-212.4541", "-1.3902", "4817395.02", "27072928.988", "S5146863", "2022-09-07 18:41:34", "0"], ["23", "23", "{00005FED-33AE-47EB-9ED9-ECB081AAB286}", "CN2436.CNI", "20220211", "CNY", "5664.472", "5639.3577", "5844.2268", "5612.2094", "5745.3077", "80.8357", "1.4271", "19476270.02", "17785748.6284", "S5260731", "2022-02-11 19:31:55", "0"], ["24", "24", "{000063A8-9DCA-405C-A486-820FFCBC2D5E}", "h30237.CSI", "20231103", "HKD", "854.7583", null, null, null, "884.5175", "29.7592", "3.4816", "8093079.53", "18917965.003", "S4641905", "2023-11-03 19:17:22", "0"], ["25", "25", "{00006A41-5E4F-45D4-AFE7-4BE33A5920FE}", "983012.CNI", "20241204", "HKD", "1522.6429", "1519.8298", "1529.4278", "1515.1733", "1523.3809", "0.738", "0.0485", "51706275.62", "76695878.672", "S9129487", "2024-12-04 19:59:18", "0"], ["26", "26", "{00006A4C-2C38-4167-AAFF-B6A43CF28BA7}", "930732.CSI", "20241219", "CNY", "1550.528", null, null, null, "1552.1932", "1.6652", "0.1074", "32620657.72", "88383236.189", "S5481926", "2024-12-19 17:57:33", "0"], ["27", "27", "{00006F3E-86AC-43D3-9F08-DABF3F4D04E3}", "000060.SH", "20241101", "CNY", "3947.0036", "3945.6003", "4002.2658", "3943.7185", "3961.7797", "14.7761", "0.3744", "359793335.0", "405689672.431", "S3767432", "2024-11-01 18:12:18", "0"], ["28", "28", "{00006F7E-892B-4412-B530-21A8192D4D3E}", "h50023.SH", "20230213", "CNY", "7580.4453", "7589.143", "7612.5026", "7558.368", "7602.7966", "22.3513", "0.2949", "6919935.99", "3928881.486", "S4508377", "2023-02-13 17:44:51", "0"], ["29", "29", "{00007078-9748-46FC-8F17-549CBDF90795}", "L01218.CSI", "20220701", "USD", "2607.118", null, null, null, "2612.8098", "5.6918", "0.2183", "3943989.27", "1289669.524", "S3706961", "2022-07-02 09:02:50", "0"], ["30", "30", "{00007090-9AD1-4C37-B090-B821A3394FA8}", "921383.CSI", "20240603", "CNY", "1717.956", null, null, null, "1719.3636", "1.4076", "0.0819", null, null, "S10847203", "2024-06-03 18:34:18", "0"], ["31", "31", "{00007564-514D-409B-938F-2127C6F5F61F}", "h20797.CSI", "20231114", "USD", "1343.1332", null, null, null, "1333.6619", "-9.4713", "-0.7052", "7577008.07", "3597664.19", "S6111012", "2023-11-15 08:21:58", "0"], ["32", "32", "{0000775A-A793-46E2-BD47-C5D164EFBB87}", "930651.CSI", "20240206", "CNY", "4608.9541", "4553.6054", "4963.6216", "4548.3351", "4938.0685", "329.1144", "7.1408", "17622008.78", "29185236.014", "S5377347", "2024-02-06 17:44:37", "0"], ["33", "33", "{00007D3B-5E9C-4835-82BC-8A3813B7C0D4}", "h30233.CSI", "20220527", "HKD", "1780.7927", null, null, null, "1827.2595", "46.4668", "2.6093", "28358579.06", "48831571.77", "S4641901", "2022-05-27 18:10:51", "0"], ["34", "34", "{00008CFD-283A-4802-8C19-0A75D9E85A77}", "931973.CSI", "20250424", "HKD", "1266.7589", null, null, null, "1264.9786", "-1.7803", "-0.1405", "956156.61", "1696650.856", "S12474393", "2025-04-24 18:43:26", "0"], ["35", "35", "{0000952E-2BFB-431D-9638-FB04AB7B35A5}", "n20755.CSI", "20230411", "CNY", "2076.6235", null, null, null, "2070.7864", "-5.8371", "-0.2811", null, null, "S5623452", "2023-04-11 17:26:25", "0"], ["36", "36", "{0000986B-2B0B-47DE-9B6C-3FCB90F17050}", "h30271.CSI", "20241220", "CNY", "2560.9443", null, null, null, "2557.8615", "-3.0828", "-0.1204", "33203580.56", "130333590.581", "S4787132", "2024-12-20 18:02:52", "0"], ["37", "37", "{00009D86-5A4F-4810-8E83-839284D71A8D}", "h20383.CSI", "20250123", "CNY", "3076.8933", null, null, null, "3065.2548", "-11.6385", "-0.3783", "119822033.33", "179603325.489", "S5037625", "2025-01-24 09:40:43", "0"], ["38", "38", "{00009F79-244A-4371-AC5A-1521E54D1916}", "931637CNY01.CSI", "20240719", "CNY", "706.9145", null, null, null, "694.4136", "-12.5009", "-1.7684", "5588970.41", "14808388.789", "S11579604", "2024-07-19 18:09:30", "0"], ["39", "39", "{0000B8E5-EE13-42CA-AE98-C9AB412D0742}", "000941.CSI", "20240919", "CNY", "1477.681", "1485.8549", "1508.3472", "1467.7599", "1488.84", "11.159", "0.7552", "10020566.28", "17492706.396", "S6305837", "2024-09-19 17:41:44", "0"], ["40", "40", "{0000B90E-645C-4B88-83D4-6690A84BF716}", "950216CNY01.CSI", "20221107", "CNY", "727.8428", null, null, null, "731.3523", "3.5095", "0.4822", null, null, "S12474191", "2022-11-07 17:39:25", "0"], ["41", "41", "{0000BC22-7FE7-4B3A-92C7-C0DDB8C1761B}", "h00010.SH", "20221222", "CNY", "12052.0807", null, null, null, "12054.1413", "2.0606", "0.0171", null, null, "S27054", "2022-12-22 17:52:58", "0"], ["42", "42", "{0000BF84-5F23-4D4D-9F87-365CFBDF85CD}", "921480.CSI", "20240201", "CNY", "573.3978", null, null, null, "577.1988", "3.801", "0.6629", null, null, "S11108975", "2024-02-01 17:19:32", "0"], ["43", "43", "{0000C7B9-7502-4747-B0BE-4425F5424889}", "931803CNY01.CSI", "20220303", "CNY", "4016.5112", null, null, null, "3984.0546", "-32.4566", "-0.8081", null, null, "S4235554", "2022-03-03 17:59:40", "0"], ["44", "44", "{0000C894-3AF9-4E14-9295-68CA6138B311}", "931867HKD01.CSI", "20231120", "HKD", "1371.7166", null, null, null, "1384.3684", "12.6518", "0.9223", "26818300.41", "72563818.537", "S12468289", "2023-11-20 18:57:04", "0"], ["45", "45", "{0000CA8A-9462-4082-B163-74413B150815}", "930681.CSI", "20230719", "CNY", "11247.723", null, null, null, "11214.1844", "-33.5386", "-0.2982", "1413160.78", "2905021.52", "S5427156", "2023-07-19 17:20:22", "0"], ["46", "46", "{0000CE08-F511-4FE6-90BD-7009CCAC882D}", "970028.CNI", "20230515", "CNY", "2412.3574", "2420.1783", "2465.8452", "2419.3112", "2465.8452", "53.4878", "2.2172", "15274920.17", "56149939.628", "S12477180", "2023-05-15 19:26:21", "0"], ["47", "47", "{0000D6B6-D480-4526-9706-9F2575C8A1A1}", "932049CNY100.CSI", "20250407", "CNY", "1080.6404", null, null, null, "966.0851", "-114.5553", "-10.6007", "65225330.09", "41451780.958", "S28185840", "2025-04-07 19:13:15", "0"], ["48", "48", "{0000D6FB-A0A9-4B69-B138-50A7DE9BA890}", "h20785.CSI", "20230803", "CNY", "5155.3978", null, null, null, "5167.3172", "11.9194", "0.2312", "18743633.53", "8399876.277", "********", "2023-08-03 18:23:32", "0"], ["49", "49", "{0000D780-DC00-432D-9E9B-6C55FE4AB1C1}", "h20838.CSI", "20250303", "CNY", "10223.3986", null, null, null, "10258.6398", "35.2412", "0.3447", null, null, "********", "2025-03-03 17:59:03", "0"]], "shape": {"columns": 17, "rows": 5872337}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>OBJECT_ID</th>\n", "      <th>S_INFO_WINDCODE</th>\n", "      <th>TRADE_DT</th>\n", "      <th>CRNCY_CODE</th>\n", "      <th>S_DQ_PRECLOSE</th>\n", "      <th>S_DQ_OPEN</th>\n", "      <th>S_DQ_HIGH</th>\n", "      <th>S_DQ_LOW</th>\n", "      <th>S_DQ_CLOSE</th>\n", "      <th>S_DQ_CHANGE</th>\n", "      <th>S_DQ_PCTCHANGE</th>\n", "      <th>S_DQ_VOLUME</th>\n", "      <th>S_DQ_AMOUNT</th>\n", "      <th>SEC_ID</th>\n", "      <th>OPDATE</th>\n", "      <th>OPMODE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>-878160</td>\n", "      <td>399107.SZ</td>\n", "      <td>19951219</td>\n", "      <td>CNY</td>\n", "      <td>122.5500</td>\n", "      <td>122.1350</td>\n", "      <td>122.9960</td>\n", "      <td>121.8150</td>\n", "      <td>122.5500</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>999881.00</td>\n", "      <td>2.512740e+05</td>\n", "      <td>2C02</td>\n", "      <td>2025-02-25 17:10:13</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>{00000267-A236-44EE-BA58-1BFA1627023A}</td>\n", "      <td>CN6046.CNI</td>\n", "      <td>20231113</td>\n", "      <td>CNY</td>\n", "      <td>1895.9237</td>\n", "      <td>1899.2997</td>\n", "      <td>1905.0503</td>\n", "      <td>1875.6984</td>\n", "      <td>1889.9613</td>\n", "      <td>-5.9624</td>\n", "      <td>-0.3145</td>\n", "      <td>17671669.80</td>\n", "      <td>2.314381e+07</td>\n", "      <td>S4593506</td>\n", "      <td>2023-11-13 19:44:26</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>{000003E4-27CB-444E-8686-91352BC35BFA}</td>\n", "      <td>h40001.SH</td>\n", "      <td>20230418</td>\n", "      <td>CNY</td>\n", "      <td>3650.3086</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3677.6290</td>\n", "      <td>27.3204</td>\n", "      <td>0.7484</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>S4118097</td>\n", "      <td>2023-04-18 17:35:56</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>{0000060C-C985-4069-9245-1264C9086DD3}</td>\n", "      <td>h40021.SH</td>\n", "      <td>20230206</td>\n", "      <td>CNY</td>\n", "      <td>8423.2663</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>8361.0967</td>\n", "      <td>-62.1696</td>\n", "      <td>-0.7381</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>S4535053</td>\n", "      <td>2023-02-06 17:35:48</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>{00000ABC-FD3B-4CC2-8191-E7C4B8DA5C0B}</td>\n", "      <td>h30461.CSI</td>\n", "      <td>20230227</td>\n", "      <td>CNY</td>\n", "      <td>3506.0440</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3487.2811</td>\n", "      <td>-18.7629</td>\n", "      <td>-0.5352</td>\n", "      <td>8314459.25</td>\n", "      <td>2.978478e+07</td>\n", "      <td>S5146829</td>\n", "      <td>2023-02-27 18:03:23</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5872332</th>\n", "      <td>5872332</td>\n", "      <td>{FFFFE3B8-DECB-484E-B4A8-201E942669A2}</td>\n", "      <td>h00978.CSI</td>\n", "      <td>20240311</td>\n", "      <td>CNY</td>\n", "      <td>12476.3758</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12765.7228</td>\n", "      <td>289.3470</td>\n", "      <td>2.3192</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>S3976712</td>\n", "      <td>2024-03-11 17:29:14</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5872333</th>\n", "      <td>5872333</td>\n", "      <td>{FFFFE88C-2224-4406-B5AF-8FB6293ECC40}</td>\n", "      <td>931957.CSI</td>\n", "      <td>20230117</td>\n", "      <td>HKD</td>\n", "      <td>2464.3324</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2448.4876</td>\n", "      <td>-15.8448</td>\n", "      <td>-0.6430</td>\n", "      <td>2928342.28</td>\n", "      <td>3.025437e+06</td>\n", "      <td>S12474333</td>\n", "      <td>2023-01-17 18:14:10</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5872334</th>\n", "      <td>5872334</td>\n", "      <td>{FFFFEE7C-EEC7-423F-A1E6-8CA23AA82262}</td>\n", "      <td>h20965.CSI</td>\n", "      <td>20230406</td>\n", "      <td>HKD</td>\n", "      <td>3313.2359</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3395.4262</td>\n", "      <td>82.1903</td>\n", "      <td>2.4807</td>\n", "      <td>5705757.27</td>\n", "      <td>7.412950e+06</td>\n", "      <td>S7720355</td>\n", "      <td>2023-04-06 18:22:44</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5872335</th>\n", "      <td>5872335</td>\n", "      <td>{FFFFF4EC-BF50-4E0A-8A04-7E973636031F}</td>\n", "      <td>931780.CSI</td>\n", "      <td>20220518</td>\n", "      <td>CNY</td>\n", "      <td>1770.8678</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1756.7495</td>\n", "      <td>-14.1183</td>\n", "      <td>-0.7973</td>\n", "      <td>6776640.40</td>\n", "      <td>2.229225e+07</td>\n", "      <td>S12074168</td>\n", "      <td>2022-05-18 19:03:10</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5872336</th>\n", "      <td>5872336</td>\n", "      <td>{FFFFFD62-8835-4E07-A975-899059E8C98C}</td>\n", "      <td>483024.CNI</td>\n", "      <td>20241029</td>\n", "      <td>HKD</td>\n", "      <td>4682.3283</td>\n", "      <td>4710.9620</td>\n", "      <td>4735.2951</td>\n", "      <td>4572.2046</td>\n", "      <td>4580.7833</td>\n", "      <td>-101.5450</td>\n", "      <td>-2.1687</td>\n", "      <td>48095734.71</td>\n", "      <td>7.192614e+07</td>\n", "      <td>S8782744</td>\n", "      <td>2024-10-29 19:51:38</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5872337 rows × 17 columns</p>\n", "</div>"], "text/plain": ["         Unnamed: 0                               OBJECT_ID S_INFO_WINDCODE  \\\n", "0                 0                                 -878160       399107.SZ   \n", "1                 1  {00000267-A236-44EE-BA58-1BFA1627023A}      CN6046.CNI   \n", "2                 2  {000003E4-27CB-444E-8686-91352BC35BFA}       h40001.SH   \n", "3                 3  {0000060C-C985-4069-9245-1264C9086DD3}       h40021.SH   \n", "4                 4  {00000ABC-FD3B-4CC2-8191-E7C4B8DA5C0B}      h30461.CSI   \n", "...             ...                                     ...             ...   \n", "5872332     5872332  {FFFFE3B8-DECB-484E-B4A8-201E942669A2}      h00978.CSI   \n", "5872333     5872333  {FFFFE88C-2224-4406-B5AF-8FB6293ECC40}      931957.CSI   \n", "5872334     5872334  {FFFFEE7C-EEC7-423F-A1E6-8CA23AA82262}      h20965.CSI   \n", "5872335     5872335  {FFFFF4EC-BF50-4E0A-8A04-7E973636031F}      931780.CSI   \n", "5872336     5872336  {FFFFFD62-8835-4E07-A975-899059E8C98C}      483024.CNI   \n", "\n", "         TRADE_DT CRNCY_CODE  S_DQ_PRECLOSE  S_DQ_OPEN  S_DQ_HIGH   S_DQ_LOW  \\\n", "0        19951219        CNY       122.5500   122.1350   122.9960   121.8150   \n", "1        20231113        CNY      1895.9237  1899.2997  1905.0503  1875.6984   \n", "2        20230418        CNY      3650.3086        NaN        NaN        NaN   \n", "3        20230206        CNY      8423.2663        NaN        NaN        NaN   \n", "4        20230227        CNY      3506.0440        NaN        NaN        NaN   \n", "...           ...        ...            ...        ...        ...        ...   \n", "5872332  20240311        CNY     12476.3758        NaN        NaN        NaN   \n", "5872333  20230117        HKD      2464.3324        NaN        NaN        NaN   \n", "5872334  20230406        HKD      3313.2359        NaN        NaN        NaN   \n", "5872335  20220518        CNY      1770.8678        NaN        NaN        NaN   \n", "5872336  20241029        HKD      4682.3283  4710.9620  4735.2951  4572.2046   \n", "\n", "         S_DQ_CLOSE  S_DQ_CHANGE  S_DQ_PCTCHANGE  S_DQ_VOLUME   S_DQ_AMOUNT  \\\n", "0          122.5500       0.0000          0.0000    999881.00  2.512740e+05   \n", "1         1889.9613      -5.9624         -0.3145  17671669.80  2.314381e+07   \n", "2         3677.6290      27.3204          0.7484          NaN           NaN   \n", "3         8361.0967     -62.1696         -0.7381          NaN           NaN   \n", "4         3487.2811     -18.7629         -0.5352   8314459.25  2.978478e+07   \n", "...             ...          ...             ...          ...           ...   \n", "5872332  12765.7228     289.3470          2.3192          NaN           NaN   \n", "5872333   2448.4876     -15.8448         -0.6430   2928342.28  3.025437e+06   \n", "5872334   3395.4262      82.1903          2.4807   5705757.27  7.412950e+06   \n", "5872335   1756.7495     -14.1183         -0.7973   6776640.40  2.229225e+07   \n", "5872336   4580.7833    -101.5450         -2.1687  48095734.71  7.192614e+07   \n", "\n", "            SEC_ID               OPDATE  OPMODE  \n", "0             2C02  2025-02-25 17:10:13       1  \n", "1         S4593506  2023-11-13 19:44:26       0  \n", "2         S4118097  2023-04-18 17:35:56       0  \n", "3         S4535053  2023-02-06 17:35:48       0  \n", "4         S5146829  2023-02-27 18:03:23       0  \n", "...            ...                  ...     ...  \n", "5872332   S3976712  2024-03-11 17:29:14       0  \n", "5872333  S12474333  2023-01-17 18:14:10       0  \n", "5872334   S7720355  2023-04-06 18:22:44       0  \n", "5872335  S12074168  2022-05-18 19:03:10       0  \n", "5872336   S8782744  2024-10-29 19:51:38       0  \n", "\n", "[5872337 rows x 17 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["indexprice = pd.read_csv('indexprice.csv', encoding='gbk')\n", "indexprice"]}, {"cell_type": "code", "execution_count": 4, "id": "af4ee88a", "metadata": {}, "outputs": [], "source": ["index_dict = {\n", "    '上证指数': '000001.SH',\n", "    '深证成指': '399001.SZ',\n", "    '创业板指': '399006.SZ',\n", "    '科创50': '000688.SH',\n", "    '北证50': '899050.BJ',\n", "    '上证50': '000016.SH',\n", "    '中证A100': '000903.SH',\n", "    '沪深300': '000300.SH',\n", "    '中证A500': '000510.SH',\n", "    '中证500': '000905.SH',\n", "    '中证1000': '000852.SH',\n", "    '中证2000': '932000.CSI',\n", "    '中证全指': '000985.CSI',\n", "    '中证红利': '000922.CSI',\n", "    # '红利全收益': 'H00922.CSI',\n", "    # '红利低波': 'H30269.CSI',\n", "}"]}, {"cell_type": "code", "execution_count": 27, "id": "c7146035", "metadata": {}, "outputs": [], "source": ["# 对于上证指数，获取最近的周五收盘后的指数点位，最近五日涨跌幅，本周、本月、今年以来涨跌幅，本周内日均成交额\n", "records = {}\n", "record = {}\n", "for index_name, index_code in index_dict.items():\n", "    record = {}\n", "    price = indexprice[indexprice['S_INFO_WINDCODE'] == index_code].sort_values('TRADE_DT').reset_index(drop=True)\n", "    price['TRADE_DT'] = pd.to_datetime(price['TRADE_DT'], format='%Y%m%d')\n", "    price['week'] = price['TRADE_DT'].dt.to_period('W')\n", "    price['month'] = price['TRADE_DT'].dt.to_period('M')\n", "    price['year'] = price['TRADE_DT'].dt.to_period('Y')\n", "    record['last_price'] = round(price.iloc[-1]['S_DQ_CLOSE'],1)\n", "    record['return_last_5'] = price.iloc[-5]['S_DQ_PCTCHANGE']\n", "    record['return_last_4'] = price.iloc[-4]['S_DQ_PCTCHANGE']\n", "    record['return_last_3'] = price.iloc[-3]['S_DQ_PCTCHANGE']\n", "    record['return_last_2'] = price.iloc[-2]['S_DQ_PCTCHANGE']\n", "    record['return_last_1'] = price.iloc[-1]['S_DQ_PCTCHANGE']\n", "    record['this_week_return'] = f\"{price.groupby('week')['S_DQ_CLOSE'].last().pct_change().iloc[-1]:.2%}\"\n", "    record['this_month_return'] = f\"{price.groupby('month')['S_DQ_CLOSE'].last().pct_change().iloc[-1]:.2%}\"\n", "    record['this_year_return'] = f\"{price.groupby('year')['S_DQ_CLOSE'].last().pct_change().iloc[-1]:.2%}\"\n", "    record['this_week_avg_amount'] = f\"{price.groupby('week')['S_DQ_AMOUNT'].mean().iloc[-1]/1e5:.2f}\"\n", "    records[index_name] = record\n", "index_performance = pd.DataFrame(records).T\n", "index_performance.to_csv('index_performance.csv', encoding='gbk')"]}], "metadata": {"kernelspec": {"display_name": "common", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}