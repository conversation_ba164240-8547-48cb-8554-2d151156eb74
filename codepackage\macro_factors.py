"""
宏观经济因子计算模块
包括经济表现（生产、消费、投资、货币）和资产表现（汇率、国债、信用）因子的计算
已check
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')


class MacroEconomicFactors:
    """宏观经济因子计算类"""

    def __init__(self, database_manager, config=None):
        """
        初始化宏观经济因子计算器

        Args:
            database_manager: 数据库管理器实例
            config: 配置对象，包含宏观因子参数
        """
        self.db = database_manager
        self.config = config
    
    def calculate_production_factor(self, start_date=None, end_date=None):
        """
        计算生产因子

        基于制造业PMI构建生产因子
        PMI->计算过去十二月均值->计算同比
        若同比上升，则生产因子为1分，若同比下降，则为-1分

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含生产因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            production_config = self.config.get_config('macro_factors').get('production', {})
            pmi_window = production_config.get('pmi_window', 12)
            lookback_months = production_config.get('lookback_months', 12)  # 同比默认12个月
        else:
            pmi_window = 12
            lookback_months = 12

        pmi_data = self.db.get_macro_data(start_date, end_date)[['date', 'pmi', 'pmi_ma12']]

        # 计算同比（使用配置的回看月数）
        # 创建回看日期列
        pmi_data['date_lookback'] = pmi_data['date'] - pd.DateOffset(months=lookback_months)

        # 通过merge找到回看期前的数据
        pmi_with_yoy = pd.merge_asof(
            pmi_data,
            pmi_data[['date', 'pmi_ma12']],
            left_on='date_lookback',
            right_on='date',
            direction='backward',
            suffixes=('', '_lookback')
        )

        # 计算同比增长率
        pmi_with_yoy['pmi_ma12_yoy'] = (pmi_with_yoy['pmi_ma12'] / pmi_with_yoy['pmi_ma12_lookback'] - 1) * 100

        # 只保留需要的列
        pmi_data['pmi_ma12_yoy'] = pmi_with_yoy['pmi_ma12_yoy']

        # 生产因子得分
        pmi_data['production_score'] = np.where(
            pmi_data['pmi_ma12_yoy'] > 0, 1, -1
        )
        
        return pmi_data[['date', 'pmi_ma12_yoy', 'production_score']]
    
    def calculate_consumption_factor(self, start_date=None, end_date=None):
        """
        计算消费因子

        0.5×CPI同比平滑值+0.5×PPI同比平滑值
        通胀方向因子相较于三个月之前降低，则说明是通胀下行环境，此时看多，分数为1
        反之则说明是通胀上行环境，此时看空，分数为-1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含消费因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            consumption_config = self.config.get_config('macro_factors').get('consumption', {})
            cpi_weight = consumption_config.get('cpi_weight', 0.5)
            ppi_weight = consumption_config.get('ppi_weight', 0.5)
            lookback_months = consumption_config.get('lookback_months', 3)
        else:
            cpi_weight = 0.5
            ppi_weight = 0.5
            lookback_months = 3

        inflation_data = self.db.get_macro_data(start_date, end_date)[['date', 'cpi', 'cpi_ma12', 'ppi', 'ppi_ma12']]

        # 消费因子（使用配置的权重）
        inflation_data['consumption_factor'] = (
            cpi_weight * inflation_data['cpi_ma12'] +
            ppi_weight * inflation_data['ppi_ma12']
        )

        # 计算回看期前的消费因子（使用配置的回看月数）
        # 找到回看期前的数据
        inflation_data['date_lookback'] = inflation_data['date'] - pd.DateOffset(months=lookback_months)
        inflation_data_lookback = pd.merge_asof(
            inflation_data,
            inflation_data[['date', 'consumption_factor']],
            left_on='date_lookback',
            right_on='date',
            suffixes=('', '_lookback')
        )
        inflation_data['consumption_factor_lookback'] = inflation_data_lookback['consumption_factor_lookback']

        # 消费因子得分
        inflation_data['consumption_score'] = np.where(
            inflation_data['consumption_factor'] < inflation_data['consumption_factor_lookback'],
            1,  # 通胀下行，看多
            -1  # 通胀上行，看空
        )
        
        inflation_data = inflation_data.reset_index()
        return inflation_data[['date', 'consumption_factor', 'consumption_score']]
    
    def calculate_investment_factor(self, start_date=None, end_date=None):
        """
        计算投资因子

        中长期贷款当月值->计算过去十二个月增量->计算同比
        同比上升则看多，分数为1。反之则看空，分数为-1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含投资因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            investment_config = self.config.get_config('macro_factors').get('investment', {})
            lookback_months = investment_config.get('lookback_months', 12)  # 同比默认12个月
        else:
            lookback_months = 12

        loan_data = self.db.get_macro_data(start_date, end_date)[['date', 'loan', 'loan_sum12']]

        # 计算同比（使用配置的回看月数）
        # 创建回看日期列
        loan_data['date_lookback'] = loan_data['date'] - pd.DateOffset(months=lookback_months)

        # 通过merge找到回看期前的数据
        loan_with_yoy = pd.merge_asof(
            loan_data,
            loan_data[['date', 'loan_sum12']],
            left_on='date_lookback',
            right_on='date',
            direction='backward',
            suffixes=('', '_lookback')
        )

        # 计算同比增长率
        loan_with_yoy['loan_yoy'] = (loan_with_yoy['loan_sum12'] / loan_with_yoy['loan_sum12_lookback'] - 1) * 100
        loan_data['loan_yoy'] = loan_with_yoy['loan_yoy']

        # 投资因子得分
        loan_data['investment_score'] = np.where(
            loan_data['loan_yoy'] > 0, 1, -1
        )

        loan_data = loan_data.reset_index()
        return loan_data[['date', 'loan_yoy', 'investment_score']]
    
    def calculate_monetary_factor(self, start_date=None, end_date=None):
        """
        计算货币因子

        央行使用的货币政策工具利率OMO、短端市场利率DR007、MLF，并计算其相较于90天前的变化方向
        利率下降时记为1分，上涨记为-1分，不变记为0分，平均分数即为货币因子
        若货币因子>0，则此时判断货币政策宽松，指标分数为1，否则为-1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含货币因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            monetary_config = self.config.get_config('macro_factors').get('monetary', {})
            lookback_days = monetary_config.get('lookback_days', 90)
        else:
            lookback_days = 90

        rate_data = self.db.get_macro_data(start_date, end_date)[['date', 'omo', 'dr007', 'mlf']]

        # 计算回看天数前的利率变化方向（使用配置的回看天数）
        rate_data['date_lookback'] = rate_data['date'] - pd.DateOffset(days=lookback_days)
        rate_data = pd.merge_asof(
            rate_data,
            rate_data[['date', 'omo', 'dr007', 'mlf']],
            left_on='date_lookback',
            right_on='date',
            suffixes=('', '_lookback')
        )
        rate_data['omo_change'] = rate_data['omo'] - rate_data['omo_lookback']
        rate_data['dr007_change'] = rate_data['dr007'] - rate_data['dr007_lookback']
        rate_data['mlf_change'] = rate_data['mlf'] - rate_data['mlf_lookback']
        
        # 计算方向得分
        rate_data['omo_score'] = np.where(
            rate_data['omo_change'] < 0, 1,  # 利率下降，宽松
            np.where(rate_data['omo_change'] > 0, -1, 0)  # 利率上升，紧缩
        )
        rate_data['dr007_score'] = np.where(
            rate_data['dr007_change'] < 0, 1,  # 利率下降，宽松
            np.where(rate_data['dr007_change'] > 0, -1, 0)  # 利率上升，紧缩
        )
        rate_data['mlf_score'] = np.where(
            rate_data['mlf_change'] < 0, 1,  # 利率下降，宽松
            np.where(rate_data['mlf_change'] > 0, -1, 0)  # 利率上升，紧缩
        )

        # 货币因子
        rate_data['monetary_factor'] = (rate_data['omo_score'] + rate_data['dr007_score'] + rate_data['mlf_score']) / 3
        # 最终得分
        rate_data['monetary_score'] = np.where(
            rate_data['monetary_factor'] > 0, 1, -1
        )

        rate_data = rate_data.reset_index()
        return rate_data[['date', 'monetary_factor', 'monetary_score']]
    
    def calculate_exchange_rate_factor(self, start_date=None, end_date=None):
        """
        计算汇率因子

        汇率上行利好股市，计算过去三个月人民币对美元的汇率变化
        若汇率变化>0，则说明人民币贬值，此时看空，分数为-1
        若汇率变化<0，则说明人民币升值，此时看多，分数为1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含汇率因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            exchange_rate_config = self.config.get_config('macro_factors').get('exchange_rate', {})
            lookback_months = exchange_rate_config.get('lookback_months', 3)
        else:
            lookback_months = 3

        exchange_data = self.db.get_macro_data(start_date, end_date)[['date', 'exchange_rate']]

        # 计算回看月数前的汇率（使用配置的回看月数）
        exchange_data['date_lookback'] = exchange_data['date'] - pd.DateOffset(months=lookback_months)
        exchange_data = pd.merge_asof(
            exchange_data,
            exchange_data[['date', 'exchange_rate']],
            left_on='date_lookback',
            right_on='date',
            suffixes=('', '_lookback')
        )
        exchange_data['exchange_rate_change_3m'] = exchange_data['exchange_rate'] - exchange_data['exchange_rate_lookback']
        
        # 汇率因子得分
        exchange_data['exchange_rate_score'] = np.where(
            exchange_data['exchange_rate_change_3m'] > 0, -1,  # 人民币贬值，看空
            np.where(exchange_data['exchange_rate_change_3m'] < 0, 1, 0)  # 人民币升值，看多
        )

        exchange_data = exchange_data.reset_index()
        return exchange_data[['date', 'exchange_rate_change_3m', 'exchange_rate_score']]
    
    def calculate_bond_factor(self, start_date=None, end_date=None):
        """
        计算国债因子

        国债收益率下行利好股市，计算过去三个月国债到期收益率变化
        若国债收益率变化>0，则说明国债收益率上升，此时看空，分数为-1
        若国债收益率变化<0，则说明国债收益率下降，此时看多，分数为1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含国债因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            bond_config = self.config.get_config('macro_factors').get('bond', {})
            lookback_months = bond_config.get('lookback_months', 3)
        else:
            lookback_months = 3

        bond_data = self.db.get_macro_data(start_date, end_date)[['date', '10y_bond']]

        # 计算回看月数前的国债收益率（使用配置的回看月数）
        bond_data['date_lookback'] = bond_data['date'] - pd.DateOffset(months=lookback_months)
        bond_data = pd.merge_asof(
            bond_data,
            bond_data[['date', '10y_bond']],
            left_on='date_lookback',
            right_on='date',
            suffixes=('', '_lookback')
        )
        bond_data['bond_yield_change_3m'] = bond_data['10y_bond'] - bond_data['10y_bond_lookback']

        # 国债因子得分
        bond_data['bond_score'] = np.where(
            bond_data['bond_yield_change_3m'] > 0, -1,  # 国债收益率上升，看空
            np.where(bond_data['bond_yield_change_3m'] < 0, 1, 0)  # 国债收益率下降，看多
        )

        bond_data = bond_data.reset_index()
        return bond_data[['date', 'bond_yield_change_3m', 'bond_score']]
    
    def calculate_credit_factor(self, start_date=None, end_date=None):
        """
        计算信用因子

        使用3年期AA中短期票据收益率与3年期国开债收益率的差来刻画信用利差的变化
        若信用利差变化>0，则说明信用利差上升，此时看空，分数为-1
        若信用利差变化<0，则说明信用利差下降，此时看多，分数为1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含信用因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            credit_config = self.config.get_config('macro_factors').get('credit', {})
            lookback_months = credit_config.get('lookback_months', 3)
        else:
            lookback_months = 3

        credit_data = self.db.get_macro_data(start_date, end_date)[['date', '3y_aa_note', '3y_cdb']]

        # 计算信用利差
        credit_data['credit_spread'] = credit_data['3y_aa_note'] - credit_data['3y_cdb']

        # 计算回看月数前的信用利差（使用配置的回看月数）
        credit_data['date_lookback'] = credit_data['date'] - pd.DateOffset(months=lookback_months)
        credit_data = pd.merge_asof(
            credit_data,
            credit_data[['date', 'credit_spread']],
            left_on='date_lookback',
            right_on='date',
            suffixes=('', '_lookback')
        )
        credit_data['credit_spread_change_3m'] = credit_data['credit_spread'] - credit_data['credit_spread_lookback']

        # 信用因子得分
        credit_data['credit_score'] = np.where(
            credit_data['credit_spread_change_3m'] > 0, -1,  # 信用利差上升，看空
            np.where(credit_data['credit_spread_change_3m'] < 0, 1, 0)  # 信用利差下降，看多
        )

        credit_data = credit_data.reset_index()
        return credit_data[['date', 'credit_spread_change_3m', 'credit_score']]

    def calculate_all_macro_factors(self, start_date=None, end_date=None):
        """
        计算所有宏观经济因子

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含所有宏观经济因子得分的数据
        """
        # 计算各个因子
        production = self.calculate_production_factor(start_date, end_date)
        consumption = self.calculate_consumption_factor(start_date, end_date)
        investment = self.calculate_investment_factor(start_date, end_date)
        monetary = self.calculate_monetary_factor(start_date, end_date)
        exchange_rate = self.calculate_exchange_rate_factor(start_date, end_date)
        bond = self.calculate_bond_factor(start_date, end_date)
        credit = self.calculate_credit_factor(start_date, end_date)

        # 创建日期范围
        all_dates = set()
        for df in [production, consumption, investment, monetary,
                  exchange_rate, bond, credit]:
            if not df.empty:
                all_dates.update(df['date'].tolist())

        if not all_dates:
            return pd.DataFrame()

        result = pd.DataFrame({'date': sorted(all_dates)})

        # 合并各个因子得分
        factor_dfs = [
            (production, 'production_score'),
            (consumption, 'consumption_score'),
            (investment, 'investment_score'),
            (monetary, 'monetary_score'),
            (exchange_rate, 'exchange_rate_score'),
            (bond, 'bond_score'),
            (credit, 'credit_score')
        ]

        for df, score_col in factor_dfs:
            if not df.empty and score_col in df.columns:
                result = result.merge(
                    df[['date', score_col]],
                    on='date', how='left'
                )

        # 分别计算经济表现和资产表现得分
        economic_performance_cols = ['production_score', 'consumption_score', 'investment_score', 'monetary_score']
        asset_performance_cols = ['exchange_rate_score', 'bond_score', 'credit_score']

        # 经济表现得分（等权平均）
        available_economic_cols = [col for col in economic_performance_cols if col in result.columns]
        if available_economic_cols:
            result['economic_performance_score'] = result[available_economic_cols].mean(axis=1, skipna=True)
        else:
            result['economic_performance_score'] = 0

        # 资产表现得分（等权平均）
        available_asset_cols = [col for col in asset_performance_cols if col in result.columns]
        if available_asset_cols:
            result['asset_performance_score'] = result[available_asset_cols].mean(axis=1, skipna=True)
        else:
            result['asset_performance_score'] = 0

        # 宏观经济综合得分（等权平均）
        result['macro_score'] = (result['economic_performance_score'] + result['asset_performance_score']) / 2

        return result

    def get_factor_description(self):
        """
        获取因子描述信息

        Returns:
            dict: 因子描述字典
        """
        return {
            'production': {
                'name': '生产因子',
                'description': '基于中采制造业PMI、中采非制造业PMI、财新制造业PMI构建的生产因子',
                'data_source': '中采PMI、财新PMI数据'
            },
            'consumption': {
                'name': '消费因子',
                'description': '0.5×CPI同比平滑值+0.5×PPI同比原始值的变化方向',
                'data_source': 'CPI、PPI同比数据'
            },
            'investment': {
                'name': '投资因子',
                'description': '中长期贷款当月值计算过去十二个月增量的同比变化',
                'data_source': '央行或统计局中长期贷款数据'
            },
            'monetary': {
                'name': '货币因子',
                'description': '央行使用的货币政策工具利率与短端市场利率的变化方向',
                'data_source': 'ShiborPrices表的b_info_rate字段，CBondRepo表的b_tender_interestrate字段'
            },
            'exchange_rate': {
                'name': '汇率因子',
                'description': '过去三个月人民币对美元的汇率变化',
                'data_source': '人民币对美元汇率数据'
            },
            'bond': {
                'name': '国债因子',
                'description': '过去三个月国债到期收益率变化',
                'data_source': '国债收益率数据'
            },
            'credit': {
                'name': '信用因子',
                'description': '3年期AA中短期票据收益率与3年期国开债收益率的差值变化',
                'data_source': '债券收益率数据'
            }
        }
