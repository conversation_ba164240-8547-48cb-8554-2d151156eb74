"""
数据库连接和数据获取模块
用于从Wind数据库获取各种金融数据
已check
"""

import pandas as pd
import pymysql
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class DatabaseManager:
    """数据库管理类，负责数据库连接和数据获取"""
    
    def __init__(self, config=None):
        """
        初始化数据库连接
        
        Args:
            config: 数据库配置字典，包含host, port, user, password, database
        """
        if config is None:
            self.config = {
                'host': '*************',
                'port': 3306,
                'user': 'ct_wind_user',
                'password': 'Ctjg2025',
                'database': 'windsh'
            }
        else:
            self.config = config
        
        self.db = None
        self.connect()
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.db = pymysql.connect(**self.config)
            print("数据库连接成功")
        except Exception as e:
            print(f"数据库连接失败: {e}")
            raise
    
    def sql_to_df(self, sql_query):
        """
        执行SQL查询并返回DataFrame
        
        Args:
            sql_query: SQL查询语句
            
        Returns:
            pandas.DataFrame: 查询结果
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(sql_query)
            info = cursor.fetchall()
            cols = cursor.description
            col = [i[0] for i in cols]
            data = pd.DataFrame(info, columns=col)
            cursor.close()
            return data
        except Exception as e:
            print(f"SQL查询失败: {e}")
            raise
    
    def get_index_price_data(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        获取指数价格数据
        
        Args:
            index_code: 指数代码，默认为中证800
            start_date: 开始日期，格式'YYYYMMDD'
            end_date: 结束日期，格式'YYYYMMDD'
            
        Returns:
            pandas.DataFrame: 包含日期、开盘价、收盘价、最高价、最低价、成交量、成交额的数据
        """
        sql = f"""
        SELECT TRADE_DT as date, S_INFO_WINDCODE as code,
               S_DQ_OPEN as open, S_DQ_CLOSE as close,
               S_DQ_HIGH as high, S_DQ_LOW as low,
               S_DQ_VOLUME as volume, S_DQ_AMOUNT as amount
        FROM aindexeodprices 
        WHERE S_INFO_WINDCODE = '{index_code}'
        """
        
        if start_date:
            sql += f" AND TRADE_DT >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DT <= '{end_date}'"
            
        sql += " ORDER BY TRADE_DT"
        
        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
            # 转换数值列
            numeric_cols = ['open', 'close', 'high', 'low', 'volume', 'amount']
            for col in numeric_cols:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    def get_stock_price_data(self, stock_codes=None, start_date=None, end_date=None):
        """
        获取股票价格数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 股票价格数据
        """
        sql = """
        SELECT TRADE_DT as date, S_INFO_WINDCODE as code,
               S_DQ_OPEN as open, S_DQ_CLOSE as close,
               S_DQ_HIGH as high, S_DQ_LOW as low,
               S_DQ_VOLUME as volume, S_DQ_AMOUNT as amount,
               S_DQ_PRECLOSE as pre_close, S_DQ_PCTCHANGE as pct_change,
               S_DQ_ADJOPEN as adj_open, S_DQ_ADJCLOSE as adj_close,  
               S_DQ_ADJHIGH as adj_high, S_DQ_ADJLOW as adj_low,
               S_DQ_ADJPRECLOSE as adj_pre_close, S_DQ_ADJFACTOR as adj_factor

        FROM ashareeodprices 
        WHERE 1=1
        """
        
        if stock_codes:
            codes_str = "','".join(stock_codes)
            sql += f" AND S_INFO_WINDCODE IN ('{codes_str}')"
        
        if start_date:
            sql += f" AND TRADE_DT >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DT <= '{end_date}'"
            
        sql += " ORDER BY S_INFO_WINDCODE, TRADE_DT"
        
        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
            numeric_cols = ['open', 'close', 'high', 'low', 'volume', 'amount', 'pre_close'
                            'pct_change', 'adj_open', 'adj_close', 'adj_high', 'adj_low',
                            'adj_pre_close', 'adj_factor']
            for col in numeric_cols:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    
    def get_stock_derivative_indicators(self, stock_codes=None, start_date=None, end_date=None):
        """
        获取衍生指标数据（市值、PE、PB、PS等）
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 衍生指标数据
        """
        sql = f"""
        SELECT TRADE_DT as date, S_INFO_WINDCODE as code,
               S_VAL_MV as market_value, S_DQ_MV as float_market_value, OPER_REV_TTM as operating_revenue,
               NET_ASSETS_TODAY as net_assets, net_profit_parent_comp_ttm as net_profit, 
               S_VAL_PE_TTM as pe, S_VAL_PB_NEW as pb, S_VAL_PS_TTM as ps,
               S_PQ_HIGH_52W_ as high_52w, S_PQ_LOW_52W as low_52w
        FROM ashareeodderivativeindicator 
        WHERE 1=1
        """
        
        if stock_codes:
            codes_str = "','".join(stock_codes)
            sql += f" AND S_INFO_WINDCODE IN ('{codes_str}')"

        if start_date:
            sql += f" AND TRADE_DT >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DT <= '{end_date}'"
            
        sql += " ORDER BY TRADE_DT"
        
        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
            numeric_cols = ['market_value', 'float_market_value', 'operating_revenue', 
                            'net_assets', 'net_profit', 'pe', 'pb', 'ps',
                            'high_52w', 'low_52w']
            for col in numeric_cols:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    def get_index_value_data(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        获取指数价值数据

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 指数价值数据
        """
        sql = f"""
        SELECT TRADE_DT as date, S_INFO_WINDCODE as code,
               PE_TTM as pe, PB_LF as pb, PCF_TTM as pcf, PS_TTM as ps,
               MV_TOTAL as market_value, MV_FLOAT as float_market_value,
               DIVIDEND_YIELD as dividend_yield, PEG_HIS as peg,
               TOT_SHR as total_shares, TOT_SHR_FLOAT as float_total_shares
        FROM aindexvaluation 
        WHERE S_INFO_WINDCODE = '{index_code}'
        """
        
        if start_date:
            sql += f" AND TRADE_DT >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DT <= '{end_date}'"

        sql += " ORDER BY TRADE_DT"
        
        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
            numeric_cols = ['pe', 'pb', 'pcf', 'ps', 'market_value', 'float_market_value',
                          'dividend_yield', 'peg', 'total_shares', 'float_total_shares']
            for col in numeric_cols:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    def get_index_component_data(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        获取指数成分股数据

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 指数成分股数据，包含股票代码、纳入日期、剔除日期
        """
        sql = f"""
        SELECT S_CON_WINDCODE as code, S_INFO_WINDCODE as index_code, 
        S_CON_INDATE as in_date, S_CON_OUTDATE as out_date, CUR_SIGN as cur_sign
        FROM aindexmembers
        WHERE S_INFO_WINDCODE = '{index_code}'
        """

        sql += " ORDER BY S_CON_INDATE"

        data = self.sql_to_df(sql)
        if not data.empty:
            data['in_date'] = pd.to_datetime(data['in_date'])
            data['out_date'] = pd.to_datetime(data['out_date'])

        # 根据in_date和out_date生成每日的成分股数据
        # 将所有空的 out_date 填充为当前日期
        data['out_date'] = data['out_date'].fillna(pd.Timestamp.now())
        
        # 生成每日日期列表
        data['date'] = data.apply(
            lambda row: pd.date_range(
                start=row['in_date'], 
                # 结束日期是剔除日期的前一天
                end=row['out_date'] - pd.Timedelta(days=1), 
                freq='D' # 'D' 表示每日频率
            ),
            axis=1
        )
        # 筛选其中的交易日
        trading_calendar = self.get_trading_date_range()
        data['date'] = data['date'].apply(
            lambda x: x.intersection(trading_calendar)
        )
        # 筛选起止日期之间的交易日
        if start_date and end_date:
            data['date'] = data.apply(
                lambda row: row['date'].intersection(pd.date_range(start=start_date, end=end_date)),
                axis=1
            )
        
        # 展开DataFrame
        expanded_df = data.explode('date')
        final_columns = ['code', 'date', 'index_code', 'cur_sign'] 
        component_data = expanded_df[final_columns].reset_index(drop=True)
        return component_data
    
    def get_zx_index_component_data(self, index_code='CI005101.WI'):
        """
        获取中信指数成分股数据

        Args:
            index_code: 指数代码

        Returns:
            pandas.DataFrame: 指数成分股数据，包含股票代码、纳入日期、剔除日期
        """
        sql = f"""
        SELECT S_CON_WINDCODE as code, S_INFO_WINDCODE as index_code, 
        S_CON_INDATE as in_date, S_CON_OUTDATE as out_date, CUR_SIGN as cur_sign
        FROM aindexmemberscitics
        WHERE S_INFO_WINDCODE = '{index_code}'
        """

        sql += " ORDER BY S_CON_INDATE"

        data = self.sql_to_df(sql)
        if not data.empty:
            data['in_date'] = pd.to_datetime(data['in_date'])
            data['out_date'] = pd.to_datetime(data['out_date'])

        return data
    
    def get_sw_index_component_data(self, index_code=None):
        """
        获取申万指数成分股数据

        Args:
            index_code: 指数代码

        Returns:
            pandas.DataFrame: 指数成分股数据，包含股票代码、纳入日期、剔除日期
        """
        sql = f"""
        SELECT S_INFO_WINDCODE as code, SW_IND_CODE as industry_code, 
        ENTRY_DT as in_date, REMOVE_DT as out_date, CUR_SIGN as cur_sign
        FROM ashareswindustriesclass
        WHERE 1=1
        """
        if index_code:
            sql += f" AND SW_IND_CODE = '{index_code}'"

        sql += " ORDER BY ENTRY_DT"
        data = self.sql_to_df(sql)
        if not data.empty:
            data['in_date'] = pd.to_datetime(data['in_date'])
            data['out_date'] = pd.to_datetime(data['out_date'])

        return data

    def get_industry_classified_data(self, codes=None, type='SW', level=3):
        """
        获取股票的行业分类数据

        Args:
            codes: 股票代码列表

        Returns:
            pandas.DataFrame: 行业分类数据
        """
        # 获取申万行业成分股数据
        industry_df = self.get_sw_index_component_data()

        # 根据in_date和out_date生成每日的成分股数据
        # 将所有空的 out_date 填充为当前日期
        industry_df['out_date'] = industry_df['out_date'].fillna(pd.Timestamp.now())
        
        # 生成每日日期列表
        industry_df['date'] = industry_df.apply(
            lambda row: pd.date_range(
                start=row['in_date'], 
                # 结束日期是剔除日期的前一天
                end=row['out_date'] - pd.Timedelta(days=1), 
                freq='D' # 'D' 表示每日频率
            ),
            axis=1
        )
        # 筛选其中的交易日
        trading_calendar = self.get_trading_date_range()
        industry_df['date'] = industry_df['date'].apply(
            lambda x: x.intersection(trading_calendar)
        )
        
        # 展开DataFrame
        expanded_df = industry_df.explode('date')
        final_columns = ['code', 'date', 'industry_code', 'cur_sign'] 
        daily_industry_data = expanded_df[final_columns].reset_index(drop=True)

        # 补充一级二级行业代码
        if type == 'SW':
            daily_industry_data['level1_code'] = daily_industry_data['industry_code'].str[:4]
            daily_industry_data['level2_code'] = daily_industry_data['industry_code'].str[:6]

        # 根据股票代码过滤数据
        if codes:
            daily_industry_data = daily_industry_data[daily_industry_data['code'].isin(codes)]

        return daily_industry_data

    def get_trading_date_range(self, start_date=None, end_date=None):
        """
        获取交易日期范围

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DatetimeIndex: 交易日期范围
        """
        trading_calendar = self.get_trading_calendar(start_date, end_date)
        if not trading_calendar.empty:
            trading_calendar = trading_calendar[trading_calendar['date']<=datetime.now()]
            date_range = pd.to_datetime(trading_calendar['date'].unique())
            return pd.DatetimeIndex(date_range).sort_values()
        else:
            # 如果无法获取交易日历，返回空的DatetimeIndex
            return pd.DatetimeIndex([])

    def calculate_index_roe(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算指数ROE

        指数ROE算法：2*∑（成份股净利润ttm）/∑（成份股净资产+一年前净资产)*100%

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 日频指数ROE数据
        """
        # 获取指数成分股数据
        component_data = self.get_index_component_data(index_code, start_date, end_date)
        if component_data.empty:
            print(f"未找到指数 {index_code} 的成分股数据")
            return pd.DataFrame()

        # 获取所有成分股的财务数据
        all_stock_codes = component_data['code'].unique().tolist()

        # 扩展日期范围以获取2年前的数据，尽可能取到前一年的财务数据
        if start_date:
            extended_start = (pd.to_datetime(start_date) - pd.DateOffset(years=2)).strftime('%Y%m%d')
        else:
            extended_start = None

        # 获取成分股的财务数据（净利润TTM和净资产）
        financial_data_list = []
        for stock_code in all_stock_codes:
            stock_financial = self.get_stock_derivative_indicators([stock_code], extended_start, end_date)
            if not stock_financial.empty:
                # 重命名列以匹配需要的字段
                stock_financial = stock_financial.rename(columns={
                    'code': 'stock_code',
                    'net_profit': 'net_profit_ttm',
                    'net_assets': 'total_equity'
                })
                financial_data_list.append(stock_financial[['date', 'stock_code', 'net_profit_ttm', 'total_equity']])

        if not financial_data_list:
            print("未找到财务数据")
            return pd.DataFrame()

        financial_data = pd.concat(financial_data_list, ignore_index=True)

        # 创建日期范围（基于交易日历）
        date_range = self.get_trading_date_range(start_date, end_date)

        # 计算ROE
        roe_results = []

        for current_date in date_range:
            # 获取当前日期的成分股
            current_constituents = component_data[(component_data['date'] == current_date)]['code'].tolist()

            if not current_constituents:
                continue

            # 获取当前日期的财务数据
            current_financial = financial_data[
                (financial_data['date'] == current_date) &
                (financial_data['stock_code'].isin(current_constituents))
            ].copy()

            # 获取一年前的净资产数据
            one_year_ago = current_date - pd.DateOffset(years=1)
            previous_financial = financial_data[
                (financial_data['date'] <= one_year_ago) &
                (financial_data['stock_code'].isin(current_constituents))
            ].copy()

            # 获取每只股票最接近一年前的净资产数据
            previous_equity = []
            for stock in current_constituents:
                stock_prev_data = previous_financial[previous_financial['stock_code'] == stock]
                # 剔除空值
                stock_prev_data = stock_prev_data.dropna(subset=['total_equity'])
                if not stock_prev_data.empty:
                    # 选择最接近一年前的数据
                    closest_data = stock_prev_data.loc[stock_prev_data['date'].idxmax()]
                    previous_equity.append({
                        'stock_code': stock,
                        'total_equity_prev': closest_data['total_equity']
                    })

            previous_equity_df = pd.DataFrame(previous_equity)

            # 合并当前和历史数据
            if not current_financial.empty and not previous_equity_df.empty:
                merged_data = current_financial.merge(
                    previous_equity_df, on='stock_code', how='left'
                )

                # 填充缺失的历史净资产数据（使用当前净资产）
                merged_data['total_equity_prev'] = merged_data['total_equity_prev'].fillna(
                    merged_data['total_equity']
                )

                # 计算指数ROE
                # 分子：2 * ∑（成份股净利润ttm）
                numerator = 2 * merged_data['net_profit_ttm'].sum()

                # 分母：∑（成份股净资产 + 一年前净资产）
                denominator = (merged_data['total_equity'] + merged_data['total_equity_prev']).sum()

                if denominator != 0:
                    index_roe = (numerator / denominator) * 100
                    roe_results.append({
                        'date': current_date,
                        'code': index_code,
                        'index_roe': index_roe,
                        'constituent_count': len(merged_data)
                    })

        result_df = pd.DataFrame(roe_results)
        if not result_df.empty:
            result_df = result_df.sort_values('date').reset_index(drop=True)

        return result_df
    
    def get_money_flow_data(self, start_date=None, end_date=None):
        """
        获取资金流数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 资金流数据
        """
        sql = """
        SELECT TRADE_DT as date, S_INFO_WINDCODE as code, 
        s_mfd_inflowvolume as net_inflowvolume, s_mfd_inflow as net_inflow,
        buy_value_exlarge_order as buy_value_exlarge, sell_value_exlarge_order as sell_value_exlarge,
        buy_value_large_order as buy_value_large, sell_value_large_order as sell_value_large
        FROM asharemoneyflow 
        WHERE 1=1
        """

        if start_date:
            sql += f" AND TRADE_DT >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DT <= '{end_date}'"

        sql += " ORDER BY S_INFO_WINDCODE, TRADE_DT"

        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
            numeric_cols = ['net_inflowvolume', 'net_inflow', 'buy_value_exlarge', 'sell_value_exlarge', 'buy_value_large', 'sell_value_large']
            for col in numeric_cols:
                data[col] = pd.to_numeric(data[col], errors='coerce')

        return data
    
    # def get_shibor_data(self, start_date=None, end_date=None):
    #     """
    #     获取Shibor利率数据
        
    #     Args:
    #         start_date: 开始日期
    #         end_date: 结束日期
            
    #     Returns:
    #         pandas.DataFrame: Shibor利率数据
    #     """
    #     sql = """
    #     SELECT B_INFO_RATE_DATE as date, B_INFO_RATE_TERM as term,
    #            B_INFO_RATE as rate
    #     FROM shiborprices 
    #     WHERE 1=1
    #     """
        
    #     if start_date:
    #         sql += f" AND B_INFO_RATE_DATE >= '{start_date}'"
    #     if end_date:
    #         sql += f" AND B_INFO_RATE_DATE <= '{end_date}'"
            
    #     sql += " ORDER BY B_INFO_RATE_DATE, B_INFO_RATE_TERM"
        
    #     data = self.sql_to_df(sql)
    #     if not data.empty:
    #         data['date'] = pd.to_datetime(data['date'])
    #         data['rate'] = pd.to_numeric(data['rate'], errors='coerce')
        
    #     return data
    
    # def get_repo_data(self, start_date=None, end_date=None):
    #     """
    #     获取回购利率数据
        
    #     Args:
    #         start_date: 开始日期
    #         end_date: 结束日期
            
    #     Returns:
    #         pandas.DataFrame: 回购利率数据
    #     """
    #     sql = """
    #     SELECT B_TENDER_DATE as date, B_TENDER_TERM as term,
    #            B_TENDER_INTERESTRATE as rate
    #     FROM cbondrepo 
    #     WHERE 1=1
    #     """
        
    #     if start_date:
    #         sql += f" AND B_TENDER_DATE >= '{start_date}'"
    #     if end_date:
    #         sql += f" AND B_TENDER_DATE <= '{end_date}'"
            
    #     sql += " ORDER BY B_TENDER_DATE, B_TENDER_TERM"
        
    #     data = self.sql_to_df(sql)
    #     if not data.empty:
    #         data['date'] = pd.to_datetime(data['date'])
    #         data['rate'] = pd.to_numeric(data['rate'], errors='coerce')
        
    #     return data

    def get_macro_data(self, start_date=None, end_date=None):
        """
        获取宏观经济数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 宏观经济数据
        """
        # 数据库目前取不到，从终端拉取数据替代
        # 月频数据
        macro_month_data = pd.read_excel('宏观数据.xlsx', 
                                        sheet_name='月度', 
                                        header=1)
        macro_month_data.columns = ['date', 'pmi', 'cpi', 'ppi', 'ppe_sum', 'ppe_ratio', 'loan']
        macro_month_data = macro_month_data.sort_values(by='date', inplace=False).reset_index(drop=True)
        macro_month_data['pmi_ma12'] = macro_month_data['pmi'].rolling(window=12).mean()
        macro_month_data['cpi_ma12'] = macro_month_data['cpi'].rolling(window=12).mean()
        macro_month_data['ppi_ma12'] = macro_month_data['ppi'].rolling(window=12).mean()
        macro_month_data['loan_sum12'] = macro_month_data['loan'].rolling(window=12).sum()

        # 日频数据
        macro_daily_data = pd.read_excel('宏观数据.xlsx', 
                                        sheet_name='日度', 
                                        header=1)
        macro_daily_data.columns = ['date', 'omo', 'dr007', 'mlf', 'shibor', 'shibor_3m', 'exchange_rate', '10y_bond', '3y_aa_note', '3y_cdb', 'vix']
        macro_daily_data = macro_daily_data.sort_values(by='date', inplace=False).reset_index(drop=True)
        # 0值清空，向前填充
        macro_daily_data.replace(0, np.nan, inplace=True)
        macro_daily_data.fillna(method='ffill', inplace=True)
        # 筛选其中的交易日
        trade_date = self.get_trading_calendar()
        macro_daily_data['date'] = pd.to_datetime(macro_daily_data['date'])
        macro_daily_data = macro_daily_data[macro_daily_data['date'].isin(trade_date['date'])]

        # 将月度数据扩展到交易日度数据上来
        macro_data = trade_date[trade_date['date']<=datetime.now()][['date']]
        macro_data = pd.merge_asof(macro_data, macro_month_data, on='date', direction='backward')
        macro_data = pd.merge_asof(macro_data, macro_daily_data, on='date', direction='backward')
        
        return macro_data
    
    def get_margin_trading_data(self, start_date=None, end_date=None):
        """
        获取融资融券数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 融资融券数据
        """
        sql = """
        SELECT TRADE_DT as date,
               S_MARSUM_EXCHMARKET as exchange, 
               S_MARSUM_TRADINGBALANCE as financing_balance,
               S_MARSUM_SECLENDINGBALANCE as securities_lending_balance
        FROM asharemargintradesum 
        WHERE 1=1
        """
        
        if start_date:
            sql += f" AND TRADE_DT >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DT <= '{end_date}'"
            
        sql += " ORDER BY TRADE_DT"
        
        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
            numeric_cols = ['financing_balance', 'securities_lending_balance']
            for col in numeric_cols:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    def get_trading_calendar(self, start_date=None, end_date=None):
        """
        获取交易日历
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 交易日历数据
        """
        sql = """
        SELECT TRADE_DAYS as date, S_INFO_EXCHMARKET as exchange
        FROM asharecalendar 
        WHERE S_INFO_EXCHMARKET = 'SSE'
        """
        
        if start_date:
            sql += f" AND TRADE_DAYS >= '{start_date}'"
        if end_date:
            sql += f" AND TRADE_DAYS <= '{end_date}'"
            
        sql += " ORDER BY TRADE_DAYS"
        
        data = self.sql_to_df(sql)
        if not data.empty:
            data['date'] = pd.to_datetime(data['date'])
        
        return data



    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()
            print("数据库连接已关闭")
