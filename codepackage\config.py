"""
参数配置系统
设计灵活的参数配置接口，支持参数调整和优化
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class StrategyConfig:
    """策略参数配置类"""
    
    def __init__(self, config_file=None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._get_default_config()
        
        if config_file:
            self.load_config(config_file)
    
    def _get_default_config(self):
        """
        获取默认配置
        
        Returns:
            dict: 默认配置字典
        """
        return {
            # 数据库配置
            'database': {
                'host': '*************',
                'port': 3306,
                'user': 'ct_wind_user',
                'password': 'Ctjg2025',
                'database': 'windsh'
            },
            
            # 回测配置
            'backtest': {
                'index_code': '000906.SH',  # 中证800指数
                'start_date': '2020-01-01',
                'end_date': '2024-12-31',
                'initial_capital': 1000000,  # 初始资金
                'commission_rate': 0.0003,   # 手续费率
                'money_fund_return': 0.02,   # 货币基金年化收益率
                'rebalance_frequency': 'weekly'  # 调仓频率
            },
            
            # 宏观经济因子参数
            'macro_factors': {
                'production': {
                    'enabled': True,
                    'pmi_window': 12,        # PMI均值计算窗口
                    'lookback_months': 3     # 生产方向比较的月数
                },
                'consumption': {
                    'enabled': True,
                    'cpi_weight': 0.5,       # CPI权重
                    'ppi_weight': 0.5,       # PPI权重
                    'cpi_smooth_window': 3,  # CPI平滑窗口
                    'lookback_months': 3     # 消费方向比较的月数
                },
                'investment': {
                    'enabled': True,
                    'lookback_months': 3     # 投资方向比较的月数
                },
                'monetary': {
                    'enabled': True,
                    'lookback_days': 90      # 利率变化回看天数
                },
                'exchange_rate': {
                    'enabled': True,
                    'lookback_months': 3     # 汇率变化回看月数
                },
                'bond': {
                    'enabled': True,
                    'lookback_months': 3     # 国债收益率变化回看月数
                },
                'credit': {
                    'enabled': True,
                    'lookback_months': 3     # 信用利差变化回看月数
                }
            },
            
            # 中观市场因子参数
            'meso_factors': {
                'margin_trading': {
                    'enabled': True,
                    'short_window': 120,     # 短期均值窗口
                    'long_window': 240       # 长期均值窗口
                },
                'turnover_trend': {
                    'enabled': True,
                    'ma_short': 120,         # 短期均线
                    'ma_long': 240,          # 长期均线
                    'max_min_windows': [10, 30, 60]  # 最值计算窗口
                },
                'market_money_flow': {
                    'enabled': True,
                    'short_window': 120,     # 短期均值窗口
                    'long_window': 240       # 长期均值窗口
                },
                'main_money_flow': {
                    'enabled': True,
                    'short_window': 120,     # 短期均值窗口
                    'long_window': 240       # 长期均值窗口
                },
                'vix': {
                    'enabled': True,
                    'lookback_months': 3     # VIX变化回看月数
                },
                'pcr': {
                    'enabled': True,
                    'lookback_months': 3     # PCR变化回看月数
                }
            },
            
            # 微观标的因子参数
            'micro_factors': {
                'shiller_erp': {
                    'enabled': True,
                    'lookback_years': 6,     # 席勒PE计算的回看年数
                    'inflation_rate': 0.02,  # 假设通胀率
                    'bond_yield_10y': 0.03,  # 10年期国债收益率
                    'zscore_window': 1095,   # 3年天数
                    'zscore_clip': 1.5       # z-score MAD截尾倍数
                },
                'pb_ps': {
                    'enabled': True,
                    'zscore_window': 1095,   # 3年天数
                    'zscore_clip': 1.5       # z-score MAD截尾倍数
                },
                'momentum': {
                    'enabled': True,
                    'periods': [63, 126, 252],  # 3个月、6个月、12个月
                    'zscore_window': 1095,   # 3年天数
                    'zscore_clip': 1.5       # z-score MAD截尾倍数
                },
                'roe': {
                    'enabled': True,
                    'zscore_window': 1095,   # 3年天数
                    'zscore_clip': 1.5       # z-score MAD截尾倍数
                },
                'liquidity': {
                    'enabled': True,
                    'periods': [63, 126, 252],  # 3个月、6个月、12个月
                    'zscore_window': 1095,   # 3年天数
                    'zscore_clip': 1.5       # z-score MAD截尾倍数
                }
            },
            
            # 技术面因子参数（作为微观标的因子的一部分）
            'technical_factors': {
                'bias': {
                    'enabled': True,
                    'period': 26,            # 移动平均周期
                    'threshold': 5           # BIAS阈值
                },
                'aroon': {
                    'enabled': True,
                    'period': 14,            # AROON计算周期
                    'threshold': 70          # AROON阈值
                },
                'adx': {
                    'enabled': True,
                    'period': 14             # ADX计算周期
                },
                'uo': {
                    'enabled': True,
                    'period1': 7,            # UO第一个周期
                    'period2': 14,           # UO第二个周期
                    'period3': 28,           # UO第三个周期
                    'buy_threshold': 70,     # 买入阈值
                    'sell_threshold': 50     # 卖出阈值
                },
                'atr': {
                    'enabled': True,
                    'period': 14,            # ATR计算周期
                    'multiplier': 2          # ATR倍数
                },
                'udvd': {
                    'enabled': True,
                    'period': 20             # UDVD平滑周期
                },
                'new_high_low': {
                    'enabled': True,
                    'lookback_days': 252,    # 新高新低回看天数
                    'smooth_window': 20      # 平滑窗口
                }
            },
            
            # 因子权重配置
            'factor_weights': {
                'macro_score': 1/3,
                'meso_score': 1/3,
                'micro_score': 1/3
            },
            
            # 信号配置
            'signal': {
                'signal_threshold': 0,       # 信号阈值
                'position_base': 0.5,        # 基础仓位
                'position_multiplier': 0.5   # 仓位调整倍数
            },
            
            # 输出配置
            'output': {
                'save_charts': True,
                'chart_dir': './charts/',
                'save_results': True,
                'result_dir': './results/',
                'file_prefix': 'csi800_timing'
            }
        }
    
    def load_config(self, config_file):
        """
        从文件加载配置
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 递归更新配置
            self._update_config(self.config, file_config)
            print(f"配置已从 {config_file} 加载")
            
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，使用默认配置")
        except json.JSONDecodeError as e:
            print(f"配置文件格式错误: {e}，使用默认配置")
    
    def save_config(self, config_file):
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            print(f"配置已保存到 {config_file}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def _update_config(self, base_config, update_config):
        """
        递归更新配置
        
        Args:
            base_config: 基础配置
            update_config: 更新配置
        """
        for key, value in update_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def get_config(self, section=None):
        """
        获取配置
        
        Args:
            section: 配置节名称，如果为None则返回全部配置
            
        Returns:
            dict: 配置字典
        """
        if section is None:
            return self.config
        else:
            return self.config.get(section, {})
    
    def set_config(self, section, key, value):
        """
        设置配置项
        
        Args:
            section: 配置节名称
            key: 配置键
            value: 配置值
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
    
    def get_enabled_factors(self, factor_type):
        """
        获取启用的因子列表
        
        Args:
            factor_type: 因子类型 ('valuation_factors', 'fundamental_factors', 'capital_factors', 'technical_factors')
            
        Returns:
            list: 启用的因子名称列表
        """
        factors_config = self.config.get(factor_type, {})
        enabled_factors = []
        
        for factor_name, factor_config in factors_config.items():
            if isinstance(factor_config, dict) and factor_config.get('enabled', True):
                enabled_factors.append(factor_name)
        
        return enabled_factors
    
    def create_config_template(self, output_file='config_template.json'):
        """
        创建配置模板文件
        
        Args:
            output_file: 输出文件路径
        """
        template = self._get_default_config()
        
        # 添加注释说明
        template['_comments'] = {
            'database': '数据库连接配置',
            'backtest': '回测基本参数配置',
            'valuation_factors': '估值面因子参数配置',
            'fundamental_factors': '基本面因子参数配置',
            'capital_factors': '资金面因子参数配置',
            'technical_factors': '技术面因子参数配置',
            'factor_weights': '各类因子权重配置，总和应为1',
            'signal': '交易信号生成参数',
            'output': '输出文件配置'
        }
        
        self.save_config(output_file)
        print(f"配置模板已创建: {output_file}")
    
    def validate_config(self):
        """
        验证配置的有效性
        
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证因子权重
        weights = self.config.get('factor_weights', {})
        weight_sum = sum(weights.values())
        if abs(weight_sum - 1.0) > 0.001:
            errors.append(f"因子权重总和应为1.0，当前为{weight_sum:.3f}")
        
        # 验证日期格式
        try:
            start_date = self.config['backtest']['start_date']
            end_date = self.config['backtest']['end_date']
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except (KeyError, ValueError):
            errors.append("回测开始和结束日期格式应为'YYYY-MM-DD'")
        
        # 验证数值参数
        numeric_checks = [
            ('backtest.initial_capital', lambda x: x > 0),
            ('backtest.commission_rate', lambda x: 0 <= x <= 0.01),
            ('backtest.money_fund_return', lambda x: 0 <= x <= 0.1),
        ]
        
        for path, check_func in numeric_checks:
            try:
                keys = path.split('.')
                value = self.config
                for key in keys:
                    value = value[key]
                
                if not check_func(value):
                    errors.append(f"参数 {path} 的值 {value} 不在有效范围内")
            except KeyError:
                errors.append(f"缺少必需参数: {path}")
        
        return len(errors) == 0, errors
