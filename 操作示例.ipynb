{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1340778e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["auth success \n"]}], "source": ["from jqdatasdk import *\n", "auth('19536993431','Jq*12345678')\n", "from technologyindicators import *"]}, {"cell_type": "markdown", "id": "a7392f5e", "metadata": {}, "source": ["多资产"]}, {"cell_type": "code", "execution_count": 2, "id": "0d223edd", "metadata": {}, "outputs": [], "source": ["# 获取多资产回测列表：若对应跟踪指数数据可以获得则取对应指数代码，若无法获得，则筛选出拥有十年以上数据的ETF\n", "# 获取ETF列表\n", "code_list = []\n", "etf_list = pd.read_excel('etf_list.xlsx', index_col=0)\n", "code_list += etf_list[~etf_list['是否有对应数据'].isna()]['是否有对应数据'].unique().tolist()\n", "code_list += etf_list[(etf_list['是否有对应数据'].isna())&(etf_list['start_year']<=2014)].index.unique().tolist()\n", "code_list = pd.DataFrame(code_list, columns=['code'])\n", "code_list['index_name'] = pd.merge(code_list, etf_list[['是否有对应数据','跟踪指数简称']].drop_duplicates(), left_on='code', right_on='是否有对应数据', how='left')['跟踪指数简称']\n", "code_list['etf_name'] = pd.merge(code_list, etf_list[['display_name']], left_on='code', right_index=True, how='left')['display_name']\n", "code_list.to_csv('多资产回测列表.csv', index=False, encoding='gbk')"]}, {"cell_type": "code", "execution_count": 3, "id": "2766fcb7", "metadata": {}, "outputs": [], "source": ["# 获取多资产回测数据\n", "# 遍历code_list，获取每个code的回测数据\n", "code_list = pd.read_csv('code_list.csv', encoding='gbk')\n", "code_price = []\n", "for code in code_list['code']:\n", "    data = get_price(code, start_date='2005-01-01', end_date='2025-05-31').dropna()\n", "    data = data.reset_index(names=['date'])\n", "    # 保证每个资产的行情数据包括十年以上的数据\n", "    if data['date'].min() > pd.Timestamp('2015-01-01'):\n", "        continue\n", "    data['code'] = code\n", "    code_price.append(data)\n", "multiple_asset_price = pd.concat(code_price, ignore_index=True)\n", "multiple_asset_price = multiple_asset_price.reindex(columns=['date', 'code', 'open', 'close', 'high', 'low', 'volume', 'money'])\n", "# multiple_asset_price.to_csv('multiple_asset_price.csv', index=False, encoding='gbk')"]}, {"cell_type": "code", "execution_count": 4, "id": "74a80a94", "metadata": {}, "outputs": [], "source": ["# 划分样本内和样本外数据\n", "# multiple_asset_price = pd.read_csv('multiple_asset_price.csv')\n", "multiple_asset_price_is = multiple_asset_price[(multiple_asset_price['date'] <= '2023-08-31')].reset_index(drop=True)\n", "multiple_asset_price_os = multiple_asset_price[(multiple_asset_price['date'] > '2023-08-31')].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "59dfd0eb", "metadata": {}, "outputs": [], "source": ["multiple_asset_is = TechnologyIndicators(multiple_asset_price_is)\n", "# 计算因子值\n", "multiple_asset_signal_is = multiple_asset_is.calculate_factor('input_8.xlsx')\n", "# 传递因子信号进行回测\n", "multiple_asset_backtest_output_is = multiple_asset_is.backtest(multiple_asset_signal_is, mode=1, commission=0.0003)\n", "# 计算回测后评价指标\n", "multiple_asset_metrics_is = multiple_asset_is.calculate_metrics(multiple_asset_backtest_output_is)\n", "# 计算因子N日后收益率\n", "multiple_asset_signal_returns_is = multiple_asset_is.calculate_signal_returns(multiple_asset_signal_is, [1, 3, 5, 10, 20, 30, 60, 120]).sort_values(by=['factor','code','direction','n_days'])\n", "# 保存回测结果\n", "# multiple_asset_metrics_is.to_csv('multiple_asset_metrics_is.csv', index=False)\n", "# multiple_asset_signal_returns_is.to_csv('multiple_asset_signal_returns_is.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "7331d75a", "metadata": {}, "outputs": [], "source": ["# 在全样本上绘制因子信号与价格走势图\n", "multiple_asset = TechnologyIndicators(multiple_asset_price)\n", "multiple_asset_signal = multiple_asset.calculate_factor('input_8.xlsx')\n", "for year in range(2010, 2026):\n", "    start_time = str(year) + '0101'\n", "    end_time = str(year) + '1231'\n", "    multiple_asset.plot_signals_with_price(multiple_asset_signal, start_time=start_time, end_time=end_time, save_dict='因子信号')"]}, {"cell_type": "code", "execution_count": null, "id": "66805142", "metadata": {}, "outputs": [], "source": ["# 参数寻优，目标可选：最大化Sharperatio、最大化n日后收益率最大值、均值、中位数\n", "# 对于部分资产的部分指标进行寻优\n", "# 导入需要寻优的code和factor\n", "code_factor_for_bestparam = pd.read_excel('寻优指标.xlsx')\n", "# 不同指标的寻优范围列表\n", "params_range = pd.DataFrame(columns=['factor', 'params_list_1', 'params_list_2'])\n", "params_range.loc[0] = ['BIAS', range(5, 31), range(4, 9)]\n", "params_range.loc[1] = ['AROON', range(10, 101, 5), range(50, 81, 10)]\n", "params_range.loc[2] = ['ADXTA', range(5, 81), range(0, 1)]\n", "params_range.loc[3] = ['UOTA', range(5, 16), range(30, 91, 2)]\n", "params_range.loc[4] = ['ATR', range(2, 31), np.arange(1, 6, 0.5)]\n", "params_range.loc[5] = ['RSI', range(2, 31), range(20, 81, 2)]\n", "params_range.loc[6] = ['ROC', range(5, 101), range(0, 1)]\n", "params_range.loc[7] = ['UDVD', range(5, 101), np.arange(0, 2e-3, 2e-4)]\n", "# 指标-参数组合\n", "params_list = pd.merge(code_factor_for_bestparam, params_range, on='factor') # 包含code、factor、params_list_1、params_list_2\n", "\n", "# 在样本内进行参数寻优，目标最大化n日后收益率的中位数\n", "multiple_asset_is = TechnologyIndicators(multiple_asset_price_is)\n", "param_n_return = multiple_asset_is.find_best_params_for_single_factor(params_list, best_indicator='n_return', best_direction='median')\n", "param_n_return.to_csv('multiplt_assets_param_n_return_median.csv')\n", "# 选取最优参数组合\n", "code_param_factor_list = param_n_return.groupby(['factor','code']).apply(lambda x: x[x.index == x['n_after_returns'].idxmax()], include_groups=False).reset_index()\n", "param_n_return.sort_values(by = 'n_after_returns', ascending=False)\n", "\n", "# 对于部分资产的部分指标进行回测\n", "single_code_single_factor = TechnologyIndicators(multiple_asset_price_is)\n", "# 直接传入最优参数组合进行回测\n", "single_code_single_factor_signal = single_code_single_factor.calculate_factor_for_single_code(code_param_factor_list)\n", "single_code_single_factor_backtest_output = single_code_single_factor.backtest(single_code_single_factor_signal, mode=1, commission=0.0003)\n", "single_code_single_factor_metrics = single_code_single_factor.calculate_metrics(single_code_single_factor_backtest_output)\n", "single_code_single_factor_metrics.to_csv('single_code_single_factor_metrics_median.csv', index=False)\n", "single_code_single_factor_signal_returns = single_code_single_factor.calculate_signal_returns(single_code_single_factor_signal)\n", "single_code_single_factor_signal_returns.to_csv('single_code_single_factor_signal_returns_median.csv', index=False)\n", "\n", "# 绘制因子信号与价格走势图\n", "for year in range(2010, 2026):\n", "    start_time = str(year) + '0101'\n", "    end_time = str(year) + '1231'\n", "    single_code_single_factor.plot_signals_with_price(\n", "        single_code_single_factor_signal, \n", "        start_time=start_time, \n", "        end_time=end_time, \n", "        save_dict='多资产信号图像-中位数最大化参数')"]}, {"cell_type": "code", "execution_count": null, "id": "1502bd85", "metadata": {}, "outputs": [], "source": ["# 参数寻优，目标可选：最大化Sharperatio、最大化n日后收益率最大值、均值、中位数\n", "# 对于全部资产的全部指标进行寻优\n", "param_sharpe = multiple_asset_is.find_best_params(params_range, best_indicator='sharpe')\n", "param_sharpe.to_csv('param_sharpe.csv')\n", "param_sharpe.sort_values(by = 'sharpe_ratio', ascending=False)\n", "# 筛选出最优参数后回测方式和正常传参相同"]}, {"cell_type": "code", "execution_count": null, "id": "338405e4", "metadata": {}, "outputs": [], "source": ["# 多因子合成：多因子信号打分法\n", "# 可选三种合成方式：\n", "# 1: 简单求和\n", "# 2: 持续状态合并（买入后保持1，卖出后保持-1）\n", "# 3: 多头持续合并（买入后保持n天1，卖出信号产生不直接归零）\n", "type = 3\n", "buy_threshold = 6\n", "sell_threshold = 2\n", "multiple_asset_multiple_signal_is = multiple_asset_is.merge_factors(\n", "    multiple_asset_signal_is[['code', 'date', 'BIAS_signal', 'AROON_signal', 'ADXTA_signal', 'UOTA_signal', 'ATR_signal', 'ROC_signal', 'UDVD_signal']], \n", "    buy_threshold=buy_threshold,\n", "    sell_threshold=sell_threshold,\n", "    type=type,\n", ")\n", "# 信号有效性检验\n", "multiple_asset_multiple_signal_return_is = multiple_asset_is.calculate_signal_returns(\n", "    multiple_asset_multiple_signal_is, \n", "    [1, 3, 5, 10, 20, 30, 60, 120]\n", "    ).sort_values(by=['factor','direction','n_days']).to_csv(f'multiple_asset_multiple_factor_type{type}_{buy_threshold}_{sell_threshold}.csv', index=False)\n", "# 回测结果\n", "multiple_asset_multiple_signal_mertrics_is = multiple_asset_is.calculate_metrics(multiple_asset_is.backtest(multiple_asset_multiple_signal_is, mode=1, commission=0.0003))\n", "multiple_asset_multiple_signal_return_is.sort_values(by=['code','factor','direction','n_days']).to_csv(f'multiple_asset_multiple_factor_type{type}_{buy_threshold}_{sell_threshold}_returns.csv', index=False)\n", "multiple_asset_multiple_signal_mertrics_is.to_csv(f'multiple_asset_multiple_factor_type{type}_{buy_threshold}_{sell_threshold}_metrics.csv', index=False)\n"]}, {"cell_type": "markdown", "id": "3b3f3b78", "metadata": {}, "source": ["单资产"]}, {"cell_type": "code", "execution_count": null, "id": "e9000d1a", "metadata": {}, "outputs": [], "source": ["from technologyindicators import *"]}, {"cell_type": "code", "execution_count": null, "id": "3ec2dcf1", "metadata": {}, "outputs": [], "source": ["# 从数据库中提取数据\n", "pd."]}, {"cell_type": "code", "execution_count": 2, "id": "733b6ebb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "code", "rawType": "object", "type": "string"}, {"name": "open", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "high", "rawType": "float64", "type": "float"}, {"name": "low", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}, {"name": "money", "rawType": "float64", "type": "float"}], "ref": "df81da13-2881-4557-b125-e0bbeba2fdc7", "rows": [["0", "2005-04-08 00:00:00", "000300.XSHG", "984.66", "1003.45", "1003.7", "979.53", "1476252600.0", "9151349808.0"], ["1", "2005-04-11 00:00:00", "000300.XSHG", "1003.88", "995.42", "1008.74", "992.77", "1593606600.0", "10436232248.0"], ["2", "2005-04-12 00:00:00", "000300.XSHG", "993.71", "978.7", "993.71", "978.2", "1022619300.0", "6479563495.0"], ["3", "2005-04-13 00:00:00", "000300.XSHG", "987.95", "1000.9", "1006.5", "987.95", "1607168700.0", "10029602129.0"], ["4", "2005-04-14 00:00:00", "000300.XSHG", "1004.64", "986.98", "1006.42", "985.58", "1294571000.0", "7813424514.0"], ["5", "2005-04-15 00:00:00", "000300.XSHG", "982.61", "974.08", "982.61", "971.93", "1040895500.0", "6541675456.0"], ["6", "2005-04-18 00:00:00", "000300.XSHG", "970.91", "963.77", "970.91", "958.65", "859840000.0", "5329138712.0"], ["7", "2005-04-19 00:00:00", "000300.XSHG", "962.92", "965.89", "968.87", "957.91", "921262300.0", "5768907755.0"], ["8", "2005-04-20 00:00:00", "000300.XSHG", "964.15", "950.87", "964.15", "946.2", "885070500.0", "5300003714.0"], ["9", "2005-04-21 00:00:00", "000300.XSHG", "948.86", "943.98", "955.55", "938.6", "994614500.0", "6140646108.0"], ["10", "2005-04-22 00:00:00", "000300.XSHG", "942.91", "939.1", "947.91", "934.96", "1069188400.0", "6373550345.0"], ["11", "2005-04-25 00:00:00", "000300.XSHG", "935.99", "930.07", "935.99", "920.16", "1147047800.0", "6667303132.0"], ["12", "2005-04-26 00:00:00", "000300.XSHG", "928.43", "937.08", "939.7", "924.65", "1169047100.0", "7029373085.0"], ["13", "2005-04-27 00:00:00", "000300.XSHG", "938.57", "926.6", "938.91", "925.9", "1078061100.0", "6432845906.0"], ["14", "2005-04-28 00:00:00", "000300.XSHG", "923.53", "942.07", "945.5", "914.83", "1434348600.0", "8315813742.0"], ["15", "2005-04-29 00:00:00", "000300.XSHG", "940.81", "932.39", "942.45", "929.81", "1123541900.0", "6853192443.0"], ["16", "2005-05-09 00:00:00", "000300.XSHG", "934.65", "909.17", "937.39", "909.17", "852911500.0", "4889696446.0"], ["17", "2005-05-10 00:00:00", "000300.XSHG", "905.54", "913.08", "913.39", "892.31", "1049433100.0", "5960910722.0"], ["18", "2005-05-11 00:00:00", "000300.XSHG", "911.84", "901.85", "917.22", "900.44", "904204700.0", "5128333208.0"], ["19", "2005-05-12 00:00:00", "000300.XSHG", "899.97", "885.82", "900.06", "883.51", "1022872000.0", "6107327165.0"], ["20", "2005-05-13 00:00:00", "000300.XSHG", "883.5", "887.54", "898.5", "875.58", "1124490600.0", "6228419643.0"], ["21", "2005-05-16 00:00:00", "000300.XSHG", "885.39", "875.27", "885.39", "869.33", "822428800.0", "4630088469.0"], ["22", "2005-05-17 00:00:00", "000300.XSHG", "873.08", "881.46", "888.28", "868.21", "877227300.0", "4635198616.0"], ["23", "2005-05-18 00:00:00", "000300.XSHG", "881.14", "883.2", "890.4", "871.82", "787861000.0", "4615409280.0"], ["24", "2005-05-19 00:00:00", "000300.XSHG", "882.84", "884.17", "888.02", "871.29", "814958300.0", "4263472583.0"], ["25", "2005-05-20 00:00:00", "000300.XSHG", "883.51", "882.76", "891.02", "879.18", "722667600.0", "3835476288.0"], ["26", "2005-05-23 00:00:00", "000300.XSHG", "880.28", "863.34", "880.28", "862.1", "727979500.0", "3725791093.0"], ["27", "2005-05-24 00:00:00", "000300.XSHG", "861.2", "868.46", "871.77", "855.59", "920692900.0", "4777971875.0"], ["28", "2005-05-25 00:00:00", "000300.XSHG", "867.66", "868.45", "876.3", "861.66", "723855600.0", "3742173189.0"], ["29", "2005-05-26 00:00:00", "000300.XSHG", "867.76", "857.33", "872.84", "854.96", "662278600.0", "3470051082.0"], ["30", "2005-05-27 00:00:00", "000300.XSHG", "855.6", "849.51", "864.96", "848.4", "774998300.0", "4240678764.0"], ["31", "2005-05-30 00:00:00", "000300.XSHG", "847.63", "855.61", "858.46", "842.1", "697158300.0", "3712116331.0"], ["32", "2005-05-31 00:00:00", "000300.XSHG", "856.56", "855.95", "863.2", "853.29", "704782100.0", "3601006687.0"], ["33", "2005-06-01 00:00:00", "000300.XSHG", "855.21", "837.53", "857.66", "836.04", "790230200.0", "4274074912.0"], ["34", "2005-06-02 00:00:00", "000300.XSHG", "835.47", "818.38", "835.49", "812.99", "950865700.0", "5220786229.0"], ["35", "2005-06-03 00:00:00", "000300.XSHG", "816.55", "818.03", "823.86", "807.97", "793605600.0", "4164754479.0"], ["36", "2005-06-06 00:00:00", "000300.XSHG", "816.73", "839.0", "839.15", "807.78", "900918500.0", "4685006534.0"], ["37", "2005-06-07 00:00:00", "000300.XSHG", "841.58", "837.28", "858.09", "835.18", "1306706800.0", "7248577634.0"], ["38", "2005-06-08 00:00:00", "000300.XSHG", "848.54", "905.77", "908.83", "844.47", "3290783900.0", "17414544247.0"], ["39", "2005-06-09 00:00:00", "000300.XSHG", "907.57", "912.6", "925.37", "895.04", "3314184000.0", "17782386004.0"], ["40", "2005-06-10 00:00:00", "000300.XSHG", "911.94", "894.56", "911.94", "889.1", "2239150200.0", "12006754528.0"], ["41", "2005-06-13 00:00:00", "000300.XSHG", "895.0", "892.96", "900.18", "877.69", "1562800200.0", "7944060172.0"], ["42", "2005-06-14 00:00:00", "000300.XSHG", "895.39", "883.54", "905.41", "882.86", "1434357000.0", "7692070462.0"], ["43", "2005-06-15 00:00:00", "000300.XSHG", "881.21", "866.83", "881.25", "865.14", "1145405600.0", "6272310250.0"], ["44", "2005-06-16 00:00:00", "000300.XSHG", "866.66", "879.24", "879.49", "861.83", "1104968500.0", "6115269193.0"], ["45", "2005-06-17 00:00:00", "000300.XSHG", "883.61", "880.35", "888.08", "876.04", "1496300900.0", "8285455982.0"], ["46", "2005-06-20 00:00:00", "000300.XSHG", "881.62", "906.25", "906.48", "872.56", "1899208200.0", "10290889580.0"], ["47", "2005-06-21 00:00:00", "000300.XSHG", "906.31", "896.17", "906.31", "894.39", "1277478400.0", "6890936652.0"], ["48", "2005-06-22 00:00:00", "000300.XSHG", "894.77", "900.65", "901.24", "891.93", "1047813400.0", "5882401451.0"], ["49", "2005-06-23 00:00:00", "000300.XSHG", "900.06", "893.57", "904.9", "892.83", "950930800.0", "5465028353.0"]], "shape": {"columns": 8, "rows": 4854}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>code</th>\n", "      <th>open</th>\n", "      <th>close</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>volume</th>\n", "      <th>money</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2005-04-08</td>\n", "      <td>000300.XSHG</td>\n", "      <td>984.6600</td>\n", "      <td>1003.4500</td>\n", "      <td>1003.7000</td>\n", "      <td>979.5300</td>\n", "      <td>1.476253e+09</td>\n", "      <td>9.151350e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2005-04-11</td>\n", "      <td>000300.XSHG</td>\n", "      <td>1003.8800</td>\n", "      <td>995.4200</td>\n", "      <td>1008.7400</td>\n", "      <td>992.7700</td>\n", "      <td>1.593607e+09</td>\n", "      <td>1.043623e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2005-04-12</td>\n", "      <td>000300.XSHG</td>\n", "      <td>993.7100</td>\n", "      <td>978.7000</td>\n", "      <td>993.7100</td>\n", "      <td>978.2000</td>\n", "      <td>1.022619e+09</td>\n", "      <td>6.479563e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2005-04-13</td>\n", "      <td>000300.XSHG</td>\n", "      <td>987.9500</td>\n", "      <td>1000.9000</td>\n", "      <td>1006.5000</td>\n", "      <td>987.9500</td>\n", "      <td>1.607169e+09</td>\n", "      <td>1.002960e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2005-04-14</td>\n", "      <td>000300.XSHG</td>\n", "      <td>1004.6400</td>\n", "      <td>986.9800</td>\n", "      <td>1006.4200</td>\n", "      <td>985.5800</td>\n", "      <td>1.294571e+09</td>\n", "      <td>7.813425e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4849</th>\n", "      <td>2025-03-25</td>\n", "      <td>000300.XSHG</td>\n", "      <td>3937.8975</td>\n", "      <td>3932.2951</td>\n", "      <td>3946.3574</td>\n", "      <td>3921.5287</td>\n", "      <td>1.404754e+10</td>\n", "      <td>2.392463e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4850</th>\n", "      <td>2025-03-26</td>\n", "      <td>000300.XSHG</td>\n", "      <td>3930.2073</td>\n", "      <td>3919.3566</td>\n", "      <td>3942.0260</td>\n", "      <td>3916.8607</td>\n", "      <td>1.311642e+10</td>\n", "      <td>2.209432e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4851</th>\n", "      <td>2025-03-27</td>\n", "      <td>000300.XSHG</td>\n", "      <td>3911.3841</td>\n", "      <td>3932.4117</td>\n", "      <td>3952.9852</td>\n", "      <td>3903.6520</td>\n", "      <td>1.267937e+10</td>\n", "      <td>2.321736e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4852</th>\n", "      <td>2025-03-28</td>\n", "      <td>000300.XSHG</td>\n", "      <td>3930.7763</td>\n", "      <td>3915.1662</td>\n", "      <td>3934.2336</td>\n", "      <td>3907.3840</td>\n", "      <td>1.263562e+10</td>\n", "      <td>2.133792e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4853</th>\n", "      <td>2025-03-31</td>\n", "      <td>000300.XSHG</td>\n", "      <td>3905.9815</td>\n", "      <td>3887.3056</td>\n", "      <td>3928.4482</td>\n", "      <td>3872.4030</td>\n", "      <td>1.667985e+10</td>\n", "      <td>2.684803e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4854 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           date         code       open      close       high        low  \\\n", "0    2005-04-08  000300.XSHG   984.6600  1003.4500  1003.7000   979.5300   \n", "1    2005-04-11  000300.XSHG  1003.8800   995.4200  1008.7400   992.7700   \n", "2    2005-04-12  000300.XSHG   993.7100   978.7000   993.7100   978.2000   \n", "3    2005-04-13  000300.XSHG   987.9500  1000.9000  1006.5000   987.9500   \n", "4    2005-04-14  000300.XSHG  1004.6400   986.9800  1006.4200   985.5800   \n", "...         ...          ...        ...        ...        ...        ...   \n", "4849 2025-03-25  000300.XSHG  3937.8975  3932.2951  3946.3574  3921.5287   \n", "4850 2025-03-26  000300.XSHG  3930.2073  3919.3566  3942.0260  3916.8607   \n", "4851 2025-03-27  000300.XSHG  3911.3841  3932.4117  3952.9852  3903.6520   \n", "4852 2025-03-28  000300.XSHG  3930.7763  3915.1662  3934.2336  3907.3840   \n", "4853 2025-03-31  000300.XSHG  3905.9815  3887.3056  3928.4482  3872.4030   \n", "\n", "            volume         money  \n", "0     1.476253e+09  9.151350e+09  \n", "1     1.593607e+09  1.043623e+10  \n", "2     1.022619e+09  6.479563e+09  \n", "3     1.607169e+09  1.002960e+10  \n", "4     1.294571e+09  7.813425e+09  \n", "...            ...           ...  \n", "4849  1.404754e+10  2.392463e+11  \n", "4850  1.311642e+10  2.209432e+11  \n", "4851  1.267937e+10  2.321736e+11  \n", "4852  1.263562e+10  2.133792e+11  \n", "4853  1.667985e+10  2.684803e+11  \n", "\n", "[4854 rows x 8 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["hs300 = get_price('000300.XSHG', start_date='2005-01-01', end_date='2025-03-31')\n", "hs300 = hs300.reset_index(names=['date'])\n", "hs300['code'] = '000300.XSHG'\n", "hs300 = hs300.reindex(columns=['date', 'code', 'open', 'close', 'high', 'low', 'volume', 'money'])\n", "hs300 = hs300.dropna().reset_index(drop=True)\n", "hs300"]}, {"cell_type": "code", "execution_count": null, "id": "becd7603", "metadata": {}, "outputs": [], "source": ["hs = TechnologyIndicators(hs300)\n", "start_time='20140101'\n", "end_time='20161231'\n", "# 部分指标提供了可视化功能\n", "# 单指标计算及可视化\n", "hs.calculate_UDVD([20],plot=1, start_time='20140101',end_time='20161231')\n", "# 多指标计算\n", "signal_hs = hs.calculate_factor('input_8.xlsx')\n", "# 多信号持仓及价格可视化\n", "hs.plot_signals_with_price(\n", "    signal_hs[['code', 'date', 'BIAS_signal', 'AROON_signal', 'ADXTA_signal', 'UOTA_signal', 'ATR_signal', 'ROC_signal', 'UDVD_signal']], \n", "    start_time=start_time, \n", "    end_time=end_time)"]}, {"cell_type": "code", "execution_count": 3, "id": "af8a4fe5", "metadata": {}, "outputs": [], "source": ["# 东北证券回测周期样本内\n", "# 计算因子并回测\n", "db = hs300[(hs300['date'] <= '2023-08-31') & (hs300['date'] > '2010-01-01')].reset_index(drop=True)\n", "etf_db = TechnologyIndicators(db)\n", "signal_db = etf_db.calculate_factor('input_8.xlsx')\n", "backtest_output_db = etf_db.backtest(signal_db, mode=1, commission=0.0003)\n", "metrics_db = etf_db.calculate_metrics(backtest_output_db)\n", "# metrics_db.to_csv('metrics_db_bestparam.csv', index=False)\n", "# 信号有效性检验\n", "signal_returns_db = etf_db.calculate_signal_returns(signal_db, [1, 3, 5, 10, 20, 30, 60, 120])\n", "signal_returns_db.sort_values(by=['factor','direction','n_days']).to_csv('signal_returns_db_bestparam.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d0cdb2e6", "metadata": {}, "outputs": [], "source": ["# 参数寻优，目标最大化sharpe_ratio\n", "params_list = pd.DataFrame(columns=['factor', 'params_list_1', 'params_list_2'])\n", "params_list.loc[0] = ['BIAS', range(5, 31), range(4, 9)]\n", "params_list.loc[1] = ['AROON', range(10, 101, 5), range(50, 81, 10)]\n", "params_list.loc[2] = ['ADXTA', range(5, 81), range(0, 1)]\n", "params_list.loc[3] = ['UOTA', range(5, 16), range(30, 91, 2)]\n", "params_list.loc[4] = ['ATR', range(2, 31), np.arange(1, 6, 0.5)]\n", "params_list.loc[5] = ['RSI', range(2, 31), range(20, 81, 2)]\n", "params_list.loc[6] = ['ROC', range(5, 101), range(0, 1)]\n", "params_list.loc[7] = ['UDVD', range(5, 101), np.arange(0, 2e-3, 2e-4)]\n", "param_sharpe = etf_db.find_best_params(params_list)\n", "param_sharpe.to_csv('param_sharpe.csv')\n", "param_sharpe.sort_values(by = 'sharpe_ratio', ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d3345a13", "metadata": {}, "outputs": [], "source": ["# 样本内\n", "# 多因子合成\n", "buy_threshold = 1\n", "sell_threshold = 6\n", "type = 3\n", "result_detailed = etf_db.merge_factors(\n", "    signal_db[['code', 'date', 'BIAS_signal', 'AROON_signal', 'ADXTA_signal', 'UOTA_signal', 'ATR_signal', 'ROC_signal', 'UDVD_signal']], \n", "    buy_threshold=buy_threshold,\n", "    sell_threshold=sell_threshold,\n", "    type=type,\n", "    signal_duration=10\n", ")\n", "# 信号有效性检验\n", "signal_return = etf_db.calculate_signal_returns(\n", "    result_detailed, \n", "    [1, 3, 5, 10, 20, 30, 60, 120]\n", "    ).sort_values(by=['factor','direction','n_days'])\n", "# 回测结果\n", "etf_db.calculate_metrics(etf_db.backtest(result_detailed, mode=1, commission=0.0003))"]}, {"cell_type": "code", "execution_count": null, "id": "d5a7fa45", "metadata": {}, "outputs": [], "source": ["# 多因子组合方式和阈值寻优\n", "max_sharpe = 0\n", "for type in range(1,4):\n", "    max_sharpe = 0\n", "    if type in [1,2,3]:\n", "        buy_range = range(0,7)\n", "        sell_range = range(-6,0)\n", "    elif type == 4:\n", "        buy_thresh = range(0,7)\n", "        sell_thresh = range(0,7)\n", "    for buy_thresh in buy_range:\n", "        for sell_thresh in sell_range:\n", "            # 合并信号\n", "            signal_db_merged = etf_db.merge_factors(\n", "                signal_db[['code','date','BIAS_signal', 'AROON_signal', 'ADXTA_signal', 'UOTA_signal', 'ATR_signal', 'ROC_signal', 'UDVD_signal']], \n", "                buy_threshold=buy_thresh, \n", "                sell_threshold=sell_thresh,\n", "                type=type\n", "            )\n", "            # 计算信号收益\n", "            merged_signal_sharpe = etf_db.calculate_metrics(etf_db.backtest(signal_db_merged, mode=1, commission=0.0003)).iloc[0, 2]\n", "            if merged_signal_sharpe > max_sharpe:\n", "                max_sharpe = merged_signal_sharpe\n", "                best_buy_thresh = buy_thresh\n", "                best_sell_thresh = sell_thresh\n", "    print(f'多因子组合方式：{type}，最佳买入阈值: {best_buy_thresh}, 最佳卖出阈值: {best_sell_thresh}, 120天平均收益: {max_sharpe}')"]}], "metadata": {"kernelspec": {"display_name": "quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}