"""
中证800择时策略使用示例
演示如何使用策略系统进行回测和分析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import CSI800TimingStrategy
from config import StrategyConfig
import pandas as pd
import warnings
warnings.filterwarnings('ignore')


def example_1_basic_strategy():
    """
    示例1：基本策略运行
    使用默认配置运行完整的中证800择时策略
    """
    print("=" * 60)
    print("示例1：基本策略运行")
    print("=" * 60)
    
    try:
        # 创建策略实例（使用默认配置）
        strategy = CSI800TimingStrategy()
        
        # 运行策略
        results = strategy.run_strategy()
        
        if results:
            print("\n策略运行成功！")
            print(f"回测期间共 {len(results['backtest_results'])} 个交易日")
            
            # 显示关键指标
            summary = results['strategy_summary']
            print(f"总收益率: {summary.get('total_return', 0):.2%}")
            print(f"年化收益率: {summary.get('annualized_return', 0):.2%}")
            print(f"年化波动率: {summary.get('annualized_volatility', 0):.2%}")
            print(f"夏普比率: {summary.get('sharpe_ratio', 0):.3f}")
            print(f"最大回撤: {summary.get('max_drawdown', 0):.2%}")
        
        # 关闭连接
        strategy.close()
        
    except Exception as e:
        print(f"示例1运行失败: {e}")


def example_2_custom_config():
    """
    示例2：自定义配置
    创建自定义配置文件并运行策略
    """
    print("=" * 60)
    print("示例2：自定义配置")
    print("=" * 60)
    
    try:
        # 创建自定义配置
        config = StrategyConfig()
        
        # 修改回测参数
        config.set_config('backtest', 'start_date', '2022-01-01')
        config.set_config('backtest', 'end_date', '2023-12-31')
        config.set_config('backtest', 'initial_capital', 5000000)  # 500万初始资金
        
        # 修改因子权重
        custom_weights = {
            'macro_score': 0.4,    # 增加宏观经济权重
            'meso_score': 0.3,     # 中观市场权重
            'micro_score': 0.3     # 微观标的权重
        }
        config.config['factor_weights'] = custom_weights
        
        # 保存自定义配置
        config.save_config('custom_config.json')
        
        # 使用自定义配置创建策略
        strategy = CSI800TimingStrategy('custom_config.json')
        
        # 运行策略
        results = strategy.run_strategy()
        
        if results:
            print("\n自定义配置策略运行成功！")
            summary = results['strategy_summary']
            print(f"总收益率: {summary.get('total_return', 0):.2%}")
            print(f"年化收益率: {summary.get('annualized_return', 0):.2%}")
        
        strategy.close()
        
    except Exception as e:
        print(f"示例2运行失败: {e}")


def example_3_factor_analysis():
    """
    示例3：单因子分析
    分别分析各类因子的表现
    """
    print("=" * 60)
    print("示例3：单因子分析")
    print("=" * 60)
    
    try:
        strategy = CSI800TimingStrategy()
        
        factor_types = ['macro', 'meso', 'micro']
        factor_names = ['宏观经济', '中观市场', '微观标的']
        
        results_summary = []
        
        for factor_type, factor_name in zip(factor_types, factor_names):
            print(f"\n分析{factor_name}因子...")
            
            try:
                result = strategy.run_factor_analysis(factor_type)
                
                if result and 'summary' in result:
                    summary = result['summary']
                    results_summary.append({
                        '因子类型': factor_name,
                        '总收益率': f"{summary.get('total_return', 0):.2%}",
                        '年化收益率': f"{summary.get('annualized_return', 0):.2%}",
                        '夏普比率': f"{summary.get('sharpe_ratio', 0):.3f}",
                        '最大回撤': f"{summary.get('max_drawdown', 0):.2%}"
                    })
                else:
                    print(f"{factor_name}因子分析失败")
                    
            except Exception as e:
                print(f"{factor_name}因子分析出错: {e}")
        
        # 显示汇总结果
        if results_summary:
            print("\n" + "=" * 80)
            print("单因子分析汇总")
            print("=" * 80)
            df_summary = pd.DataFrame(results_summary)
            print(df_summary.to_string(index=False))
        
        strategy.close()
        
    except Exception as e:
        print(f"示例3运行失败: {e}")


def example_4_signal_effectiveness():
    """
    示例4：信号有效性分析
    分析交易信号的前瞻性效果
    """
    print("=" * 60)
    print("示例4：信号有效性分析")
    print("=" * 60)
    
    try:
        strategy = CSI800TimingStrategy()
        
        # 先运行策略获取信号
        results = strategy.run_strategy(save_results=False)
        
        if results and 'signals' in results:
            # 分析信号有效性
            print("\n分析信号有效性...")
            effectiveness = strategy.backtest_engine.calculate_signal_effectiveness(
                results['signals']
            )
            
            if not effectiveness.empty:
                print("\n信号有效性分析结果:")
                print(effectiveness.to_string(index=False))
            else:
                print("信号有效性分析失败")
        
        strategy.close()
        
    except Exception as e:
        print(f"示例4运行失败: {e}")


def example_5_parameter_optimization():
    """
    示例5：参数优化示例
    演示如何进行简单的参数优化
    """
    print("=" * 60)
    print("示例5：参数优化示例")
    print("=" * 60)
    
    try:
        # 测试不同的因子权重组合
        weight_combinations = [
            {'macro_score': 0.5, 'meso_score': 0.3, 'micro_score': 0.2},
            {'macro_score': 0.3, 'meso_score': 0.5, 'micro_score': 0.2},
            {'macro_score': 0.3, 'meso_score': 0.2, 'micro_score': 0.5},
            {'macro_score': 1/3, 'meso_score': 1/3, 'micro_score': 1/3},
        ]
        
        optimization_results = []
        
        for i, weights in enumerate(weight_combinations):
            print(f"\n测试权重组合 {i+1}: {weights}")
            
            try:
                # 创建配置
                config = StrategyConfig()
                config.config['factor_weights'] = weights
                
                # 缩短回测期间以加快速度
                config.set_config('backtest', 'start_date', '2023-01-01')
                config.set_config('backtest', 'end_date', '2023-12-31')
                
                # 保存临时配置
                temp_config_file = f'temp_config_{i}.json'
                config.save_config(temp_config_file)
                
                # 运行策略
                strategy = CSI800TimingStrategy(temp_config_file)
                results = strategy.run_strategy(save_results=False)
                
                if results and 'strategy_summary' in results:
                    summary = results['strategy_summary']
                    optimization_results.append({
                        '权重组合': f"组合{i+1}",
                        '宏观经济权重': weights['macro_score'],
                        '中观市场权重': weights['meso_score'],
                        '微观标的权重': weights['micro_score'],
                        '年化收益率': summary.get('annualized_return', 0),
                        '夏普比率': summary.get('sharpe_ratio', 0),
                        '最大回撤': summary.get('max_drawdown', 0)
                    })
                
                strategy.close()
                
                # 删除临时配置文件
                if os.path.exists(temp_config_file):
                    os.remove(temp_config_file)
                    
            except Exception as e:
                print(f"权重组合 {i+1} 测试失败: {e}")
        
        # 显示优化结果
        if optimization_results:
            print("\n" + "=" * 100)
            print("参数优化结果")
            print("=" * 100)
            df_optimization = pd.DataFrame(optimization_results)
            
            # 格式化显示
            df_optimization['年化收益率'] = df_optimization['年化收益率'].apply(lambda x: f"{x:.2%}")
            df_optimization['夏普比率'] = df_optimization['夏普比率'].apply(lambda x: f"{x:.3f}")
            df_optimization['最大回撤'] = df_optimization['最大回撤'].apply(lambda x: f"{x:.2%}")
            
            print(df_optimization.to_string(index=False))
        
    except Exception as e:
        print(f"示例5运行失败: {e}")


def main():
    """运行所有示例"""
    print("中证800择时策略使用示例")
    print("注意：运行示例需要连接到Wind数据库")
    print()
    
    # 运行示例（可以选择性运行）
    try:
        # 基本策略运行
        example_1_basic_strategy()
        
        # 自定义配置（可选）
        # example_2_custom_config()
        
        # 单因子分析（可选）
        # example_3_factor_analysis()
        
        # 信号有效性分析（可选）
        # example_4_signal_effectiveness()
        
        # 参数优化（可选，耗时较长）
        # example_5_parameter_optimization()
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
