"""
因子综合评分系统
实现各个因子的标准化处理和综合评分计算逻辑
基于新的三层架构：宏观（经济）、中观（市场）、微观（标的）
"""

import pandas as pd
import numpy as np
from .macro_factors import MacroEconomicFactors
from .meso_factors import MesoMarketFactors
from .micro_factors import MicroTargetFactors
import warnings
warnings.filterwarnings('ignore')


def mad_clip(series, multiplier=1.5):
    """
    使用MAD（中位数绝对偏差）进行截尾处理

    Args:
        series: 待处理的数据序列
        multiplier: MAD倍数，默认1.5倍

    Returns:
        截尾后的数据序列
    """
    median = series.median()
    mad = np.median(np.abs(series - median))

    # 避免MAD为0的情况
    if mad == 0:
        return series

    # 计算截尾边界
    lower_bound = median - multiplier * mad
    upper_bound = median + multiplier * mad

    return np.clip(series, lower_bound, upper_bound)


class FactorScoring:
    """因子综合评分系统"""

    def __init__(self, database_manager, config=None):
        """
        初始化因子评分系统

        Args:
            database_manager: 数据库管理器实例
            config: 配置对象，包含因子参数
        """
        self.db = database_manager
        self.config = config
        self.macro_factors = MacroEconomicFactors(database_manager, config)
        self.meso_factors = MesoMarketFactors(database_manager, config)
        self.micro_factors = MicroTargetFactors(database_manager, config)
    
    def calculate_all_factors(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算所有因子得分
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 包含所有因子得分的数据
        """
        print("正在计算宏观因子...")
        macro_scores = self.macro_factors.calculate_all_macro_factors(
            start_date, end_date
        )
        
        print("正在计算中观因子...")
        meso_scores = self.meso_factors.calculate_all_meso_factors(
            index_code, start_date, end_date
        )
        
        print("正在计算微观因子...")
        micro_scores = self.micro_factors.calculate_all_micro_factors(
            index_code, start_date, end_date
        )
        
        # 合并所有因子得分
        if not macro_scores.empty:
            result = macro_scores[['date', 'code']].copy()
        elif not meso_scores.empty:
            result = meso_scores[['date', 'code']].copy()
        elif not micro_scores.empty:
            result = micro_scores[['date', 'code']].copy()
        else:
            return pd.DataFrame()
        
        # 合并宏观得分
        if not macro_scores.empty:
            result = result.merge(
                macro_scores[['date', 'macro_score']], 
                on='date', how='left'
            )
        else:
            result['macro_score'] = 0
        
        # 合并中观得分
        if not meso_scores.empty:
            result = result.merge(
                meso_scores[['date', 'meso_score']], 
                on='date', how='left'
            )
        else:
            result['meso_score'] = 0
        
        # 合并微观得分
        if not micro_scores.empty:
            result = result.merge(
                micro_scores[['date', 'micro_score']], 
                on='date', how='left'
            )
        else:
            result['micro_score'] = 0
        
        # 填充缺失值
        score_cols = ['macro_score', 'meso_score', 'micro_score']
        for col in score_cols:
            if col in result.columns:
                result[col] = result[col].fillna(0)
        
        return result
    
    def calculate_comprehensive_score(self, factor_scores, weights=None):
        """
        计算综合得分
        
        Args:
            factor_scores: 包含各因子得分的DataFrame
            weights: 权重字典，默认等权
            
        Returns:
            pandas.DataFrame: 包含综合得分的数据
        """
        if factor_scores.empty:
            return pd.DataFrame()
        
        # 默认等权重
        if weights is None:
            weights = {
                'macro_score': 1/3,
                'meso_score': 1/3,
                'micro_score': 1/3
            }
        
        result = factor_scores.copy()
        
        # 计算综合得分
        comprehensive_score = 0
        total_weight = 0
        
        for factor, weight in weights.items():
            if factor in result.columns:
                comprehensive_score += result[factor] * weight
                total_weight += weight
        
        if total_weight > 0:
            result['comprehensive_score'] = comprehensive_score / total_weight
        else:
            result['comprehensive_score'] = 0
        
        return result
    
    def generate_trading_signals(self, comprehensive_scores, signal_threshold=0):
        """
        生成交易信号
        
        根据综合得分生成交易信号，用于周度调仓
        
        Args:
            comprehensive_scores: 包含综合得分的DataFrame
            signal_threshold: 信号阈值
            
        Returns:
            pandas.DataFrame: 包含交易信号的数据
        """
        if comprehensive_scores.empty:
            return pd.DataFrame()
        
        result = comprehensive_scores.copy()
        
        # 生成交易信号
        result['signal'] = np.where(
            result['comprehensive_score'] > signal_threshold, 1,  # 看多信号
            np.where(result['comprehensive_score'] < -signal_threshold, -1, 0)  # 看空信号
        )
        
        # 计算持仓权重
        # 持有权益的仓位 = 0.5 + 指标分数 × 0.5
        result['equity_weight'] = 0.5 + result['comprehensive_score'] * 0.5
        
        # 确保权重在0-1之间
        result['equity_weight'] = np.clip(result['equity_weight'], 0, 1)
        
        # 货币基金权重
        result['money_fund_weight'] = 1 - result['equity_weight']
        
        return result
    
    def get_weekly_signals(self, signals_df):
        """
        获取周度信号
        
        
        使用每周最后一个交易日信号，用其后一个交易日的收盘价进行买卖
        
        Args:
            signals_df: 包含交易信号的DataFrame
            
        Returns:
            pandas.DataFrame: 周度交易信号
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        result = signals_df.copy()
        result['date'] = pd.to_datetime(result['date'])
        
        # 添加周信息
        result['year_week'] = result['date'].dt.strftime('%Y-%U')
        # 添加执行日期
        result['execution_date'] = result['date'].shift(-1)
        
        # 获取每周最后一个交易日的信号
        weekly_signals = result.groupby('year_week').last().reset_index()
        
        return weekly_signals
    
    def standardize_scores(self, scores_df, method='zscore', window=252):
        """
        标准化因子得分
        
        Args:
            scores_df: 包含因子得分的DataFrame
            method: 标准化方法，'zscore'或'minmax'
            window: 滚动窗口大小
            
        Returns:
            pandas.DataFrame: 标准化后的得分
        """
        if scores_df.empty:
            return pd.DataFrame()
        
        result = scores_df.copy()
        score_cols = [col for col in result.columns if col.endswith('_score')]
        
        for col in score_cols:
            if method == 'zscore':
                # 滚动Z-score标准化
                rolling_mean = result[col].rolling(window, min_periods=30).mean()
                rolling_std = result[col].rolling(window, min_periods=30).std()
                result[f'{col}_standardized'] = (result[col] - rolling_mean) / rolling_std
                
                # 处理无穷大和NaN值
                result[f'{col}_standardized'] = result[f'{col}_standardized'].replace(
                    [np.inf, -np.inf], np.nan
                ).fillna(0)
                
                # MAD截尾处理（3倍MAD）
                result[f'{col}_standardized'] = mad_clip(
                    result[f'{col}_standardized'], multiplier=3
                )
                
            elif method == 'minmax':
                # 滚动Min-Max标准化
                rolling_min = result[col].rolling(window, min_periods=30).min()
                rolling_max = result[col].rolling(window, min_periods=30).max()
                result[f'{col}_standardized'] = (
                    (result[col] - rolling_min) / (rolling_max - rolling_min) * 2 - 1
                )
                
                
                # 处理除零错误
                result[f'{col}_standardized'] = result[f'{col}_standardized'].fillna(0)
        
        return result
    
    def get_factor_summary(self, factor_scores):
        """
        获取因子得分汇总统计
        
        Args:
            factor_scores: 包含因子得分的DataFrame
            
        Returns:
            pandas.DataFrame: 因子统计汇总
        """
        if factor_scores.empty:
            return pd.DataFrame()
        
        score_cols = [col for col in factor_scores.columns if col.endswith('_score')]
        
        summary_stats = []
        for col in score_cols:
            stats = {
                'factor': col,
                'mean': factor_scores[col].mean(),
                'std': factor_scores[col].std(),
                'min': factor_scores[col].min(),
                'max': factor_scores[col].max(),
                'positive_ratio': (factor_scores[col] > 0).mean(),
                'negative_ratio': (factor_scores[col] < 0).mean(),
                'zero_ratio': (factor_scores[col] == 0).mean()
            }
            summary_stats.append(stats)
        
        return pd.DataFrame(summary_stats)
