"""
参数配置使用示例
展示如何使用新的参数配置功能来自定义因子计算
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from codepackage.main import CSI800TimingStrategy
from codepackage.config import StrategyConfig

def example_1_basic_usage():
    """示例1：基本使用 - 使用默认配置"""
    print("=" * 60)
    print("示例1：使用默认配置运行策略")
    print("=" * 60)
    
    # 使用默认配置
    strategy = CSI800TimingStrategy()
    
    # 运行策略
    results = strategy.run_strategy()
    print("策略运行完成！")
    return results

def example_2_custom_config_file():
    """示例2：使用自定义配置文件"""
    print("=" * 60)
    print("示例2：使用自定义配置文件")
    print("=" * 60)
    
    # 使用自定义配置文件
    strategy = CSI800TimingStrategy('example_config.json')
    
    # 运行策略
    results = strategy.run_strategy()
    print("策略运行完成！")
    return results

def example_3_programmatic_config():
    """示例3：程序化修改配置"""
    print("=" * 60)
    print("示例3：程序化修改配置")
    print("=" * 60)
    
    # 创建配置对象
    config = StrategyConfig()
    
    # 修改宏观因子配置
    config.set_config('macro_factors', 'production', {'enabled': True, 'pmi_window': 6, 'lookback_months': 6})
    config.set_config('macro_factors', 'credit', {'enabled': False})  # 禁用信用因子
    
    # 修改中观因子配置
    config.set_config('meso_factors', 'vix', {'enabled': False})  # 禁用VIX因子
    
    # 修改微观因子配置
    config.set_config('micro_factors', 'momentum', {'enabled': True, 'periods': [30, 60, 120], 'zscore_clip': 2.0})
    
    # 修改技术因子配置
    config.set_config('technical_factors', 'bias', {'enabled': True, 'period': 20, 'threshold': 3})
    config.set_config('technical_factors', 'aroon', {'enabled': False})  # 禁用AROON因子
    
    # 修改因子权重
    config.set_config('factor_weights', 'macro_score', 0.5)
    config.set_config('factor_weights', 'meso_score', 0.3)
    config.set_config('factor_weights', 'micro_score', 0.2)
    
    # 保存配置
    config.save_config('custom_config.json')
    
    # 使用修改后的配置创建策略
    strategy = CSI800TimingStrategy('custom_config.json')
    
    # 运行策略
    results = strategy.run_strategy()
    print("策略运行完成！")
    return results

def example_4_factor_analysis():
    """示例4：单因子分析"""
    print("=" * 60)
    print("示例4：单因子分析")
    print("=" * 60)
    
    # 创建配置，只启用特定因子
    config = StrategyConfig()
    
    # 禁用所有宏观因子，只保留生产因子
    macro_factors = ['production', 'consumption', 'investment', 'monetary', 'exchange_rate', 'bond', 'credit']
    for factor in macro_factors:
        if factor == 'production':
            config.set_config('macro_factors', factor, {'enabled': True})
        else:
            config.set_config('macro_factors', factor, {'enabled': False})
    
    # 禁用所有中观因子
    meso_factors = ['margin_trading', 'turnover_trend', 'market_money_flow', 'main_money_flow', 'vix']
    for factor in meso_factors:
        config.set_config('meso_factors', factor, {'enabled': False})
    
    # 禁用所有微观因子
    micro_factors = ['pb_ps', 'momentum', 'roe', 'liquidity']
    for factor in micro_factors:
        config.set_config('micro_factors', factor, {'enabled': False})
    
    # 禁用所有技术因子
    technical_factors = ['bias', 'aroon', 'adx', 'uo', 'atr', 'udvd', 'new_high_low']
    for factor in technical_factors:
        config.set_config('technical_factors', factor, {'enabled': False})
    
    # 保存配置
    config.save_config('production_factor_only.json')
    
    # 使用配置创建策略
    strategy = CSI800TimingStrategy('production_factor_only.json')
    
    # 运行单因子分析
    results = strategy.run_factor_analysis('macro')
    print("生产因子分析完成！")
    return results

def example_5_parameter_sensitivity():
    """示例5：参数敏感性分析"""
    print("=" * 60)
    print("示例5：参数敏感性分析")
    print("=" * 60)
    
    # 测试不同的PMI窗口参数
    pmi_windows = [6, 9, 12, 15, 18]
    results = {}
    
    for window in pmi_windows:
        print(f"测试PMI窗口: {window}个月")
        
        # 创建配置
        config = StrategyConfig()
        config.set_config('macro_factors', 'production', {
            'enabled': True, 
            'pmi_window': window, 
            'lookback_months': 3
        })
        
        # 保存配置
        config_file = f'pmi_window_{window}.json'
        config.save_config(config_file)
        
        # 运行策略
        strategy = CSI800TimingStrategy(config_file)
        result = strategy.run_strategy()
        results[window] = result
        
        print(f"PMI窗口{window}个月测试完成")
    
    print("参数敏感性分析完成！")
    return results

if __name__ == "__main__":
    # 运行示例
    print("开始运行参数配置示例...")
    
    # 示例1：基本使用
    # example_1_basic_usage()
    
    # 示例2：使用配置文件
    # example_2_custom_config_file()
    
    # 示例3：程序化配置
    example_3_programmatic_config()
    
    # 示例4：单因子分析
    # example_4_factor_analysis()
    
    # 示例5：参数敏感性分析
    # example_5_parameter_sensitivity()
    
    print("所有示例运行完成！")
