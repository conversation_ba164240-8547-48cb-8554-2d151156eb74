"""
中证800择时策略主程序
整合所有模块，提供完整的策略运行框架
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from factor_scoring import FactorScoring
from backtest import BacktestEngine
from visualization import Visualization
from config import StrategyConfig


class CSI800TimingStrategy:
    """中证800择时策略主类"""
    
    def __init__(self, config_file=None):
        """
        初始化策略
        
        Args:
            config_file: 配置文件路径
        """
        print("=" * 60)
        print("中证800择时策略系统")
        print("=" * 60)
        
        # 加载配置
        self.config = StrategyConfig(config_file)
        
        # 验证配置
        is_valid, errors = self.config.validate_config()
        if not is_valid:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            raise ValueError("配置文件存在错误")
        
        # 初始化各个模块
        print("正在初始化数据库连接...")
        self.db = DatabaseManager(self.config.get_config('database'))
        
        print("正在初始化因子计算模块...")
        self.factor_scoring = FactorScoring(self.db)
        
        print("正在初始化回测引擎...")
        self.backtest_engine = BacktestEngine(self.db)
        
        print("正在初始化可视化模块...")
        self.visualization = Visualization()
        
        print("初始化完成！\n")
    
    def run_strategy(self, save_results=True):
        """
        运行完整策略
        
        Args:
            save_results: 是否保存结果
            
        Returns:
            dict: 策略运行结果
        """
        backtest_config = self.config.get_config('backtest')
        output_config = self.config.get_config('output')
        
        print("开始运行中证800择时策略...")
        print(f"回测期间: {backtest_config['start_date']} 至 {backtest_config['end_date']}")
        print(f"目标指数: {backtest_config['index_code']}")
        print()
        
        # 第一步：计算所有因子得分
        print("第一步：计算因子得分")
        factor_scores = self.factor_scoring.calculate_all_factors(
            index_code=backtest_config['index_code'],
            start_date=backtest_config['start_date'],
            end_date=backtest_config['end_date']
        )
        
        if factor_scores.empty:
            print("错误：无法获取因子数据")
            return {}
        
        print(f"因子数据计算完成，共 {len(factor_scores)} 个交易日")
        
        # 第二步：计算综合得分
        print("\n第二步：计算综合得分")
        factor_weights = self.config.get_config('factor_weights')
        comprehensive_scores = self.factor_scoring.calculate_comprehensive_score(
            factor_scores, factor_weights
        )
        
        # 第三步：生成交易信号
        print("第三步：生成交易信号")
        signal_config = self.config.get_config('signal')
        signals = self.factor_scoring.generate_trading_signals(
            comprehensive_scores, signal_config['signal_threshold']
        )
        
        # 第四步：运行回测
        print("第四步：运行策略回测")
        backtest_results = self.backtest_engine.run_backtest(
            signals, backtest_config['index_code']
        )
        
        if backtest_results.empty:
            print("错误：回测失败")
            return {}
        
        # 第五步：获取基准数据
        print("第五步：计算基准表现")
        benchmark_data = self.backtest_engine.get_benchmark_data(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )
        
        # 第六步：计算业绩指标
        print("第六步：计算业绩指标")
        strategy_summary = self.backtest_engine.get_backtest_summary(backtest_results)
        
        # 计算基准业绩（简化处理）
        if not benchmark_data.empty:
            benchmark_summary = {
                'total_return': benchmark_data['benchmark_nav'].iloc[-1] - 1,
                'annualized_return': (benchmark_data['benchmark_nav'].iloc[-1] ** (252 / len(benchmark_data))) - 1,
                'annualized_volatility': benchmark_data['benchmark_return'].std() * np.sqrt(252),
                'max_drawdown': ((benchmark_data['benchmark_nav'] / benchmark_data['benchmark_nav'].expanding().max()) - 1).min()
            }
        else:
            benchmark_summary = {}
        
        # 第七步：生成报告
        print("第七步：生成分析报告")
        performance_report = self.visualization.create_performance_report(
            strategy_summary, benchmark_summary
        )
        
        # 打印业绩报告
        print("\n" + "=" * 50)
        print("策略业绩报告")
        print("=" * 50)
        print(performance_report.to_string(index=False))
        print()
        
        # 第八步：保存结果和图表
        if save_results and output_config['save_results']:
            print("第八步：保存结果文件")
            self._save_results(
                factor_scores, signals, backtest_results, 
                performance_report, output_config
            )
        
        if output_config['save_charts']:
            print("第九步：生成并保存图表")
            self.visualization.save_all_charts(
                backtest_results, benchmark_data, factor_scores,
                output_config['chart_dir'], output_config['file_prefix']
            )
        
        print("\n策略运行完成！")
        
        return {
            'factor_scores': factor_scores,
            'signals': signals,
            'backtest_results': backtest_results,
            'benchmark_data': benchmark_data,
            'strategy_summary': strategy_summary,
            'benchmark_summary': benchmark_summary,
            'performance_report': performance_report
        }
    
    def run_factor_analysis(self, factor_type='all'):
        """
        运行单因子分析

        Args:
            factor_type: 因子类型 ('macro', 'meso', 'micro', 'all')

        Returns:
            dict: 因子分析结果
        """
        backtest_config = self.config.get_config('backtest')

        print(f"开始运行{factor_type}因子分析...")

        if factor_type == 'macro':
            factor_scores = self.factor_scoring.macro_factors.calculate_all_macro_factors(
                backtest_config['start_date'], backtest_config['end_date']
            )
        elif factor_type == 'meso':
            factor_scores = self.factor_scoring.meso_factors.calculate_all_meso_factors(
                backtest_config['index_code'], backtest_config['start_date'], backtest_config['end_date']
            )
        elif factor_type == 'micro':
            factor_scores = self.factor_scoring.micro_factors.calculate_all_micro_factors(
                backtest_config['index_code'], backtest_config['start_date'], backtest_config['end_date']
            )
        else:
            return self.run_strategy()
        
        if factor_scores.empty:
            print(f"错误：无法获取{factor_type}因子数据")
            return {}

        # 生成单因子信号和回测
        score_col = f'{factor_type}_score'
        if score_col in factor_scores.columns:
            signals = factor_scores.copy()
            signals['comprehensive_score'] = signals[score_col]
            signals = self.factor_scoring.generate_trading_signals(signals)

            backtest_results = self.backtest_engine.run_backtest(
                signals, backtest_config['index_code']
            )

            summary = self.backtest_engine.get_backtest_summary(backtest_results)

            print(f"\n{factor_type}因子分析完成")
            print(f"总收益率: {summary.get('total_return', 0):.2%}")
            print(f"年化收益率: {summary.get('annualized_return', 0):.2%}")
            print(f"夏普比率: {summary.get('sharpe_ratio', 0):.3f}")
            print(f"最大回撤: {summary.get('max_drawdown', 0):.2%}")

            return {
                'factor_scores': factor_scores,
                'signals': signals,
                'backtest_results': backtest_results,
                'summary': summary
            }

        return {}
    
    def _save_results(self, factor_scores, signals, backtest_results, 
                     performance_report, output_config):
        """
        保存结果文件
        
        Args:
            factor_scores: 因子得分
            signals: 交易信号
            backtest_results: 回测结果
            performance_report: 业绩报告
            output_config: 输出配置
        """
        result_dir = output_config['result_dir']
        prefix = output_config['file_prefix']
        
        os.makedirs(result_dir, exist_ok=True)
        
        # 保存因子得分
        factor_scores.to_csv(f'{result_dir}/{prefix}_factor_scores.csv', index=False, encoding='utf-8-sig')
        
        # 保存交易信号
        signals.to_csv(f'{result_dir}/{prefix}_signals.csv', index=False, encoding='utf-8-sig')
        
        # 保存回测结果
        backtest_results.to_csv(f'{result_dir}/{prefix}_backtest_results.csv', index=False, encoding='utf-8-sig')
        
        # 保存业绩报告
        performance_report.to_csv(f'{result_dir}/{prefix}_performance_report.csv', index=False, encoding='utf-8-sig')
        
        print(f"结果文件已保存到 {result_dir} 目录")
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'db'):
            self.db.close()


def main():
    """主函数"""
    try:
        # 创建策略实例
        strategy = CSI800TimingStrategy()
        
        # 运行策略
        results = strategy.run_strategy()
        
        # 可选：运行单因子分析
        # valuation_results = strategy.run_factor_analysis('valuation')
        # fundamental_results = strategy.run_factor_analysis('fundamental')
        # capital_results = strategy.run_factor_analysis('capital')
        # technical_results = strategy.run_factor_analysis('technical')
        
    except Exception as e:
        print(f"策略运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        if 'strategy' in locals():
            strategy.close()


if __name__ == "__main__":
    main()
