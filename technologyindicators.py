import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
plt.style.use('ggplot')
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
import seaborn as sns
import datetime as dt
import talib as ta

def short_cross_long(shortline, longline):
    '''
    短期均线与长期均线交叉策略

    :param shortline: 短期均线数据
    :param longline: 长期均线数据
    :return signal: 交易信号序列，1表示买入，-1表示卖出，0表示无操作
    '''
    signal = np.where((shortline >= longline)& (shortline.shift(1) < longline.shift(1)), 1, 0)
    signal = np.where((shortline <= longline)& (shortline.shift(1) > longline.shift(1)), -1, signal)
    return signal

def up_cross_threshold_buy(indicator,buy_threshold,sell_threshold):
    """
    基于阈值上下穿的信号生成函数：上穿买入，下穿卖出
    
    :param indicator: 指标序列 (如MACD线)
    :param buy_threshold: 上穿触发买入的阈值
    :param sell_threshold: 下穿触发卖出的阈值
    :return signal: 交易信号序列，1表示买入，-1表示卖出，0表示无操作
    """
    signal = pd.Series(0, index=indicator.index, dtype=int)
    
    # 处理上穿买入逻辑
    signal = np.where((indicator >= buy_threshold) & (indicator.shift(1) < buy_threshold), 1, signal)
    
    # 处理下穿卖出逻辑（反向条件）
    signal = np.where((indicator <= sell_threshold) & (indicator.shift(1) > sell_threshold), -1, signal)
    
    return signal

def down_cross_threshold_buy(indicator,buy_threshold,sell_threshold):
    """
    基于阈值上下穿的信号生成函数：下穿买入，上穿卖出
    
    :param indicator: 指标序列 (如MACD线)
    :param buy_threshold: 触发买入的阈值
    :param sell_threshold: 触发卖出的阈值
    :return signal: 交易信号序列，1表示买入，-1表示卖出，0表示无操作
    """
    signal = pd.Series(0, index=indicator.index, dtype=int)
    
    # 处理下穿买入逻辑
    signal = np.where((indicator <= buy_threshold) & (indicator.shift(1) > buy_threshold), 1, signal)
    
    # 处理上穿卖出逻辑（反向条件）
    signal = np.where((indicator >= sell_threshold) & (indicator.shift(1) < sell_threshold), -1, signal)
    
    return signal

def cross_upper_lower(mid, upper, lower):
    """
    中轨与上下轨交叉策略，中轨上穿上轨买入，下穿下轨卖出

    :param mid: 中轨数据
    :param upper: 上轨数据
    :param lower: 下轨数据
    :return: 交易信号序列，1表示买入，-1表示卖出，0表示无操作
    """
    signal = np.where((mid >= upper) & (mid.shift(1) < upper.shift(1)), 1, 0)
    signal = np.where((mid <= lower) & (mid.shift(1) > lower.shift(1)), -1, signal)
    return signal

def ema_ewm(series, n):
    """
    使用滑动窗口计算EMA，每次取n天数据用ewm计算

    :param series: 输入的价格序列 
    :param n: EMA的周期
    :return ema_values: 包含EMA值的Series
    """
    # 初始化结果序列
    ema_values = np.zeros(len(series))
    
    # 处理第一个值
    ema_values[0] = series.iloc[0]
    
    # 循环计算每个时点的EMA
    for i in range(1, len(series)):
        if i < n:
            # 当i小于n时，使用所有可用数据
            window = series.iloc[:i+1]
        else:
            # 使用过去n天的数据
            window = series.iloc[i-n+1:i+1]
        
        # 使用ewm计算当前窗口的EMA
        # span=n表示使用n天的指数权重
        # adjust=False表示不调整初始观察值
        # min_periods=1表示至少需要1个有效值就开始计算
        ema_values[i] = window.ewm(span=n, adjust=False, min_periods=1).mean().iloc[-1]
    
    return pd.Series(ema_values, index=series.index, name=f'EMA_{n}')

def draw_advanced_histogram(series_data):
    """
    绘制一个Series的分布图，包括直方图、核密度估计曲线、描述性统计信息等。

    :param series_data: Series, 要绘制的Series数据
    """
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 7))

    # 绘制高级直方图
    sns.histplot(
        series_data, 
        kde=True,  # 添加核密度估计曲线
        stat="density",  # 使用密度而不是计数
        alpha=0.6,  # 透明度
        line_kws={"linewidth": 2}  # 密度曲线样式
    )

    # 添加标题和标签
    plt.title('Distribution of '+series_data.name, fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('Value', fontsize=14, labelpad=10)
    plt.ylabel('Density', fontsize=14, labelpad=10)

    # 添加网格线
    # plt.grid(True, alpha=0.3, linestyle='--')

    # 添加描述性统计信息
    stats_text = (
        f"Mean: {series_data.mean():.2f}\n"
        f"Median: {series_data.median():.2f}\n"
        f"Std Dev: {series_data.std():.2f}\n"
        f"Min: {series_data.min():.2f}\n"
        f"Max: {series_data.max():.2f}"
    )
    plt.annotate(
        stats_text, 
        xy=(0.02, 0.85), 
        xycoords='axes fraction',
        bbox=dict(boxstyle="round,pad=0.5", fc="#f8f9fa", ec="gray", alpha=0.8),
        fontsize=12
    )

    # 美化图表边框
    sns.despine(left=False, bottom=False)
    plt.show()
    # plt.savefig('distribution/distribution_'+series_data.name+'.png', dpi=300)

def process_signals_with_position_constraint(signal_db):
    """
    为信号数据库中的每一列信号添加持仓限制条件：卖出后才能进一步买入
    
    :param signal_db: DataFrame, 包含多个信号列的数据框
    
    :return result_db: DataFrame, 包含处理后信号的数据框
    """
    # 创建一个新的DataFrame来存储结果
    result_db = signal_db.copy()
    
    # 为每个信号列应用持仓限制
    for col in signal_db.columns[2:]:
        # 获取原始信号
        original_signal = signal_db[col].values
        
        # 创建新的信号数组
        new_signal = np.zeros(len(signal_db))
        
        # 使用一个临时变量跟踪持仓状态
        position = 0  # 0表示无持仓，1表示有持仓
        
        # 遍历每个时间点
        for i in range(len(signal_db)):
            # 根据当前持仓状态和信号决定新的信号
            
            # 如果当前无持仓
            if position == 0:
                # 当前是买入信号
                if original_signal[i] == 1:
                    new_signal[i] = 1  # 保留买入信号
                    position = 1       # 更新持仓状态
                # 当前是卖出信号，但已经没有持仓，忽略
                elif original_signal[i] == -1:
                    new_signal[i] = 0  # 忽略不必要的卖出信号
                # 当前无信号
                else:
                    new_signal[i] = 0
            
            # 如果当前有持仓
            else:  # position == 1
                # 当前是买入信号，但已经有持仓，忽略
                if original_signal[i] == 1:
                    new_signal[i] = 0  # 忽略重复买入信号
                # 当前是卖出信号
                elif original_signal[i] == -1:
                    new_signal[i] = -1  # 保留卖出信号
                    position = 0        # 更新持仓状态
                # 当前无信号
                else:
                    new_signal[i] = 0
        
        # 将新的信号添加到结果DataFrame
        result_db[col] = new_signal
    
    return result_db


class TechnologyIndicators:
    '''
    TechnologyIndicators类，用于计算ETF的技术指标、产生交易信号，并进行后续的回测和调参等操作
    '''
    def __init__(self, price):
        '''
        Signal类的初始化函数，接收一个包含ETF价格数据的DataFrame

        :param price: 包含ETF价格数据的DataFrame，必须包含'code'、'date'和'close'列
        '''
        self.price = price.sort_values(by=['code', 'date'])
        self.price['date'] = pd.to_datetime(self.price['date'])
        self.price = self.price.set_index('date')

    def calculate_factor(self, input_file):
        '''
        计算技术指标，产生交易信号

        :param input_file: 包含因子名称和参数的Excel文件路径
        :return factor_list: 包含计算得到的因子信号的DataFrame
        '''
        signal_params = self.load_signal_params(input_file)
        signal = pd.DataFrame(self.price[['code']].reset_index())

        # 存储所有code的信号结果
        all_signals = []
        # 按code分组处理
        for code in signal['code'].unique():
            # 获取当前资产的数据
            code_price = self.price[self.price['code'] == code]

            # 创建当前code的信号DataFrame
            code_signal = pd.DataFrame({
                'date': code_price.index,
                'code': code
            })

            # 临时替换self.price为当前code的数据（用于因子计算）
            original_price = self.price
            self.price = code_price

            # 计算因子信号
            for signal_param in signal_params:
                factor_name = signal_param['name']
                factor_type = signal_param['type']
                params = signal_param['params']
                if factor_type == 1:
                    factor_function = 'calculate_' + factor_name
                    # 计算短线和长线指标
                    short_line = eval('self.' + factor_function + '(params[0])')
                    long_line = eval('self.' + factor_function + '(params[1])')
                    
                    # # 仅获取当前code的信号
                    # code_short = short_line[signal['code'] == code]
                    # code_long = long_line[signal['code'] == code]
                    
                    # # 生成信号
                    # code_signal = short_cross_long(code_short, code_long)
                    # signal.loc[signal['code'] == code, factor_name+'_signal'] = code_signal
                    
                    code_signal[factor_name+'_signal'] = short_cross_long(short_line, long_line)
                elif factor_type == 2:
                    factor_function = 'calculate_' + factor_name
                    # 计算指标值
                    indicator = eval('self.' + factor_function + '(params)')
                    code_signal[factor_name+'_signal'] = up_cross_threshold_buy(indicator, signal_param['buy_threshold'], signal_param['sell_threshold'])
                elif factor_type == 3:
                    # 股价上穿上轨买入，下穿下轨卖出
                    factor_function = 'calculate_' + factor_name
                    # 计算上下轨
                    upper, lower = eval('self.' + factor_function + '(params)')
                    close = self.price['close'].reset_index(drop=True)
                    # 计算买卖信号
                    code_signal[factor_name+'_signal'] = cross_upper_lower(close, upper, lower)
                elif factor_type == 4:
                    factor_function = 'get_' + factor_name + '_signal'
                    code_signal[factor_name+'_signal'] = eval('self.' + factor_function + '(params, signal_param["buy_threshold"], signal_param["sell_threshold"])')
            # 恢复原始价格数据
            self.price = original_price
            
            # 添加到结果列表
            all_signals.append(code_signal)
        # 合并所有code的信号结果
        signal = pd.concat(all_signals, ignore_index=True)
        # 按日期和代码排序
        signal = signal.sort_values(['code', 'date']).reset_index(drop=True)
        
        self.signal = signal
        return signal
    
    def calculate_factor_for_single_code(self, code_factor_param_signal):
        '''
        计算单个代码-指标对的技术指标，产生交易信号

        :param code_factor_param_signal: 包含单个代码-指标对信息的DataFrame，必须包含'code'、'factor'、'param_1'和'param_2'列
        :return factor_list: 包含计算得到的因子信号的DataFrame
        '''

        # 存储所有code的信号结果
        all_signals = []
        # 按code分组处理
        for code in code_factor_param_signal['code'].unique():
            # 获取当前资产的数据
            code_price = self.price[self.price['code'] == code]

            # 创建当前code的信号DataFrame
            code_signal = pd.DataFrame({
                'date': code_price.index,
                'code': code
            })

            # 临时替换self.price为当前code的数据（用于因子计算）
            original_price = self.price
            self.price = code_price

            for factor_name in code_factor_param_signal[code_factor_param_signal['code'] == code]['factor'].unique():
                param_1 = code_factor_param_signal[(code_factor_param_signal['code'] == code) & (code_factor_param_signal['factor'] == factor_name)]['param_1'].values[0]
                param_2 = code_factor_param_signal[(code_factor_param_signal['code'] == code) & (code_factor_param_signal['factor'] == factor_name)]['param_2'].values[0]
                if factor_name in ['ADXTA']:
                    factor_function = 'calculate_' + factor_name
                    
                    # 计算短线和长线指标
                    param_1_ = [param_1, 1]
                    param_2_ = [param_1, 2]
                    short_line = eval('self.' + factor_function + '(param_1_)')
                    long_line = eval('self.' + factor_function + '(param_2_)')

                    code_signal[factor_name+'_signal'] = short_cross_long(short_line, long_line)

                elif factor_name in ['BIAS', 'RSI', 'ROC', 'UOTA', 'UDVD']:
                    factor_function = 'calculate_' + factor_name
                    # 计算指标值
                    if factor_name in ['ROC', 'UOTA']:
                        # 寻优参数不包括阈值
                        if factor_name in ['UOTA']:
                            params = [param_1, param_1*2, param_2]
                        else:
                            params = [param_1]
                        buy_threshold = self._get_default_threshold(factor_name, 'buy')
                        sell_threshold = self._get_default_threshold(factor_name, 'sell')
                    elif factor_name in ['BIAS', 'RSI', 'UDVD']:
                        # 寻优参数包括阈值
                        params = [param_1]
                        if factor_name in ['BIAS', 'UDVD']:
                            buy_threshold = param_2
                            sell_threshold = -param_2
                        elif factor_name in ['RSI']:
                            buy_threshold = param_2
                            sell_threshold = 100 - param_2
                    indicator = eval('self.' + factor_function + '(params)')
                    # 计算买卖信号
                    code_signal[factor_name+'_signal'] = up_cross_threshold_buy(indicator, buy_threshold, sell_threshold)
                elif factor_name in ['ATR']:
                    factor_function = 'calculate_' + factor_name
                    # 计算上下轨
                    if factor_name in ['ATR']:
                        params = [param_1, param_2]
                    # 计算买卖信号
                    upper, lower = eval('self.' + factor_function + '(params)')
                    close = self.price['close'].reset_index(drop=True)
                    # 计算买卖信号
                    code_signal[factor_name+'_signal'] = cross_upper_lower(close, upper, lower)
                elif factor_name in ['AROON']:
                    factor_function = 'get_' + factor_name + '_signal'
                    if factor_name in ['AROON']:
                        params = [param_1]
                        buy_threshold = param_2
                        sell_threshold = param_2
                    # 计算买卖信号
                    params = [param_1, param_2]
                    buy_threshold = self._get_default_threshold(factor_name, 'buy')
                    sell_threshold = self._get_default_threshold(factor_name, 'sell')
                    # 计算买卖信号
                    code_signal[factor_name+'_signal'] = eval('self.' + factor_function + '(params, buy_threshold, sell_threshold)')

            # 恢复原始价格数据
            self.price = original_price
            
            # 添加到结果列表
            all_signals.append(code_signal)
        # 合并所有code的信号结果
        signal = pd.concat(all_signals, ignore_index=True)
        # 按日期和代码排序
        signal = signal.sort_values(['code', 'date']).reset_index(drop=True)
        
        self.signal = signal
        return signal

    def load_signal_params(self, filepath):
        """
        从Excel读取信号参数
        
        :param filepath: Excel文件路径
        :return signal_params: 包含信号参数的字典列表
        """
        # 读取Excel并保留原始数据类型
        df = pd.read_excel(
            filepath,
            dtype={
                'Factor': str,
                'Type': 'Int64',  # 允许空值
                'Var_short': object,
                'Var_long': object,
                'Threshold_buy': object,
                'Threshold_sell': object
            }
        ).fillna('')  # 空单元格转为空字符串

        signal_params = []
        for _, row in df.iterrows():
            # 基础参数校验
            if not row['Factor'] or pd.isna(row['Type']):
                continue

            # 初始化参数结构
            param_dict = {
                'name': row['Factor'].strip().upper(),
                'type': int(row['Type']),
                'params': [],
                'buy_threshold': None,
                'sell_threshold': None
            }

            try:
                # 根据信号类型处理参数
                if param_dict['type'] == 1:
                    # 类型1：参数1=短线周期，参数2=长线周期
                    param_temp = []
                    if isinstance(row['Var_short'], str) and ',' in row['Var_short']:
                        param_temp.append(self._parse_param_list(
                            row['Var_short'],
                            defaults=self._get_default_params(param_dict['name'], param_dict['type'])[0]
                            )
                        )
                    else:
                        param_temp.append([self._parse_number(row['Var_short'])])
                    if isinstance(row['Var_long'], str) and ',' in row['Var_long']:
                        param_temp.append(self._parse_param_list(
                            row['Var_long'],
                            defaults=self._get_default_params(param_dict['name'], param_dict['type'])[1]
                            )
                        )
                    else:
                        param_temp.append([self._parse_number(row['Var_long'])])
                    param_dict['params'] = param_temp

                    
                elif param_dict['type'] == 2 or param_dict['type'] == 3 or param_dict['type'] == 4:
                    # 类型2：参数1=指标参数，参数2无效
                    if isinstance(row['Var_short'], str) and ',' in row['Var_short']:
                        param_dict['params'] = self._parse_param_list(
                            row['Var_short'],
                            defaults=self._get_default_params(param_dict['name'], param_dict['type'])
                        )
                    else:
                        param_dict['params'] = [self._parse_number(row['Var_short'])]
                    # 处理阈值
                    param_dict['buy_threshold'] = self._parse_number(
                        row['Threshold_buy'],
                        default=self._get_default_threshold(param_dict['name'], 'buy')
                    )
                    param_dict['sell_threshold'] = self._parse_number(
                        row['Threshold_sell'],
                        default=self._get_default_threshold(param_dict['name'], 'sell')
                    )

                signal_params.append(param_dict)
            except ValueError as e:
                print(f"参数解析错误（行{_+2}）：{str(e)}")
                continue

        return signal_params

    def _parse_number(self, value, default=None):
        """解析单个数值参数"""   
         # 处理空值
        if pd.isna(value) or value in ['', None]:
            return default
        
        try:
            num = float(value)
            # 判断是否为整数
            if num.is_integer():
                return int(num)
            return num
        except (ValueError, TypeError):
            return default

    def _parse_param_list(self, param_str, defaults=None):
        """解析逗号分隔的数值列表"""
        if not param_str and defaults:
            return defaults.copy()
        
        params = []
        for p in str(param_str).split(','):
            p = p.strip()
            if p:
                try:
                    params.append(float(p) if '.' in p else int(p))
                except:
                    raise ValueError(f"非法参数值：{p}")
        return params or defaults.copy()

    def _get_default_params(self, signal_name, signal_type):
        """默认参数映射"""
        return {
            ('SMA', 1):    [[5], [20]],
            ('EMA', 1):    [[10], [20]],
            ('KAMA', 1): [[10], [20]],
            ('MACD', 2):   [12, 26, 9],
            ('DPO', 2):   [20],
            ('AROON', 3): [20],
            ('ADX', 1): [[14,1], [14, 2]],
            ('ADXTA', 1): [[14,1], [14, 2]],
            ('SAR', 1): [[0.02, 0.2, 1], [0.02, 0.2, 2]],
            ('MOM', 2): [10],
            ('BIAS', 2): [26],
            ('RSI', 2): [14],
            ('RSITA', 2): [14],
            ('KDJ', 1): [[9,3,1],[9,3,2]],
            ('ROC', 2): [20],
            ('CCI', 2): [14],
            ('CCITA', 2): [14],
            ('CMO', 2): [25],
            ('CMOTA', 2): [25],
            ('UO', 2): [7,14,28],
            ('UOTA', 2): [7,14,28],
            ('TRIX', 1): [[12, 10, 1], [12, 20, 2]],
            ('TRIXTA', 1): [[12, 20, 1], [12, 20, 2]],
            ('ATR', 3): [14,2],
            ('ATRTA', 3): [14,2],
            ('BOLL', 3): [20, 2],
            ('DC', 3): [20],
            ('ACCBANDS', 3): [20],
            ('UDVD', 2): [20],
            ('OBV', 1): [[10], [20]],
            ('EOM', 2): [20],
            ('MAAMT', 1): [[30,1], [30, 2]],
            ('FI', 2): [13]
        }.get((signal_name, signal_type), [])

    def _get_default_threshold(self, signal_name, side):
        """默认阈值映射"""
        return {
            ('MACD', 'buy'): 0.0,
            ('MACD', 'sell'): 0.0,
            ('DPO', 'buy'): 0.0,
            ('DPO', 'sell'): 0.0,
            ('AROON', 'buy'): 70.0,
            ('AROON', 'sell'): 70.0,
            ('MOM', 'buy'): 0.0,
            ('MOM', 'sell'): 0.0,
            ('BIAS', 'buy'): 5.0,
            ('BIAS', 'sell'): -5.0,
            ('RSI', 'buy'): 30.0,
            ('RSI', 'sell'): 70.0,
            ('RSITA', 'buy'): 30.0,
            ('RSITA', 'sell'): 70.0,
            ('KDJ', 'buy'): 20.0,
            ('KDJ', 'sell'): 80.0,
            ('ROC', 'buy'): 0.0,
            ('ROC', 'sell'): 0.0,
            ('CCI', 'buy'): 100.0,
            ('CCI', 'sell'): -100.0,
            ('CCITA', 'buy'): 100.0,
            ('CCITA', 'sell'): -100.0,
            ('CMO', 'buy'): 0.0,
            ('CMO', 'sell'): 0.0,
            ('CMOTA', 'buy'): 0.0,
            ('CMOTA', 'sell'): 0.0,
            ('UOTA', 'buy'): 70.0,
            ('UOTA', 'sell'): 50.0,
            ('UDVD', 'buy'): 0.0,
            ('UDVD', 'sell'): 0.0,
            ('EOM', 'buy'): 0.0,
            ('EOM', 'sell'): 0.0,
            ('FI', 'buy'): 0.0,
            ('FI', 'sell'): 0.0
        }.get((signal_name, side), None)
    
    def find_best_params(self, parameters_df, rf_rate=0.02, best_indicator='sharpe', best_direction='max'):
        '''
        对于所有资产寻找所有技术指标最佳参数

        :param parameters_list: 参数列表，包括因子名称、两个可选参数列表
        :param rf_rate: 无风险利率
        :param best_indicator: 评价指标，可选'n_return'、'sharpe'
        :param best_direction: n日后收益率聚合方式，可选'max'、'min'、'avg'、'median'
        :return: 最佳参数列表
        '''
        # 获取参数列表
        parameters_list = parameters_df.to_dict(orient='records')
        # 创建记录所有参数组合夏普比率的列表
        sharpe_records = []
        for row in parameters_list:
            factor_name = row['factor']
            params_list_1 = row['params_list_1']
            params_list_2 = row['params_list_2']
            for code in self.price['code'].unique():
                # 获取当前资产的数据
                code_price = self.price[self.price['code'] == code]

                # 临时替换self.price为当前code的数据（用于因子计算）
                original_price = self.price
                self.price = code_price

                for param_1 in params_list_1:
                    for param_2 in params_list_2:
                        signal = pd.DataFrame(self.price[['code']].reset_index())
                        if factor_name in ['SMA', 'EMA', 'KAMA', 'ADX', 'ADXTA', 'TRIX','TRIXTA', 'OBV', 'MAAMT']:
                            # # 跳过短期参数大于等于长期参数的情况
                            # if short_period >= long_period:
                            #     continue
                            factor_function = 'calculate_' + factor_name
                            
                            # 计算短线和长线指标
                            param_1_ = [param_1]
                            param_2_ = [param_2]
                            if factor_name in ['ADX', 'ADXTA']:
                                param_1_ = [param_1, 1]
                                param_2_ = [param_1, 2]
                            elif factor_name in ['MAAMT']:
                                param_1_ = [param_1, 1]
                                param_2_ = [param_1, 2]
                            elif factor_name in ['TRIX','TRIXTA']:
                                param_1_ = [param_1, param_2, 1]
                                param_2_ = [param_1, param_2, 2]
                            short_line = eval('self.' + factor_function + '(param_1_)')
                            long_line = eval('self.' + factor_function + '(param_2_)')

                            signal[factor_name+'_signal'] = short_cross_long(short_line, long_line)

                        elif factor_name in ['MACD','MACDTA', 'DPO', 'MOM', 'BIAS', 'RSI', 'RSITA', 'ROC', 'CCI','CCITA', 'CMO', 'CMOTA', 'UO', 'UOTA', 'UDVD', 'EOM', 'FI']:
                            factor_function = 'calculate_' + factor_name
                            # 计算指标值
                            if factor_name in ['MACD','MACDTA', 'DPO', 'MOM', 'ROC', 'CMO', 'CMOTA', 'UO', 'UOTA', 'EOM', 'FI']:
                                # 寻优参数不包括阈值
                                if factor_name in ['MACD','MACDTA']:
                                    params = [param_1, param_2, 9]
                                elif factor_name in ['UO', 'UOTA']:
                                    params = [param_1, param_1*2, param_2]
                                else:
                                    params = [param_1]
                                buy_threshold = self._get_default_threshold(factor_name, 'buy')
                                sell_threshold = self._get_default_threshold(factor_name, 'sell')
                            elif factor_name in ['BIAS', 'RSI', 'RSITA', 'CCI', 'CCITA', 'UDVD']:
                                # 寻优参数包括阈值
                                params = [param_1]
                                if factor_name in ['BIAS', 'CCI', 'CCITA', 'UDVD']:
                                    buy_threshold = param_2
                                    sell_threshold = -param_2
                                elif factor_name in ['RSI', 'RSITA']:
                                    buy_threshold = param_2
                                    sell_threshold = 100 - param_2
                            indicator = eval('self.' + factor_function + '(params)')
                            # 计算买卖信号
                            signal[factor_name+'_signal'] = up_cross_threshold_buy(indicator, buy_threshold, sell_threshold)
                        elif factor_name in ['ATR', 'ATRTA', 'BOLL', 'DC', 'ACCBANDS']:
                            factor_function = 'calculate_' + factor_name
                            # 计算上下轨
                            if factor_name in ['ATR', 'ATRTA', 'BOLL']:
                                params = [param_1, param_2]
                            elif factor_name in ['DC', 'ACCBANDS']:
                                params = [param_1]
                            # 计算买卖信号
                            upper, lower = eval('self.' + factor_function + '(params)')
                            close = self.price['close'].reset_index(drop=True)
                            # 计算买卖信号
                            signal[factor_name+'_signal'] = cross_upper_lower(close, upper, lower)
                        elif factor_name in ['AROON', 'KDJ']:
                            factor_function = 'get_' + factor_name + '_signal'
                            if factor_name in ['AROON']:
                                params = [param_1]
                                buy_threshold = param_2
                                sell_threshold = param_2
                            elif factor_name in ['KDJ']:
                                params = [param_1, round(param_1/3, 0)]
                                sell_threshold = param_2
                                buy_threshold = 100 - param_2
                            # 计算买卖信号
                            params = [param_1, param_2]
                            buy_threshold = self._get_default_threshold(factor_name, 'buy')
                            sell_threshold = self._get_default_threshold(factor_name, 'sell')
                            # 计算买卖信号
                            signal[factor_name+'_signal'] = eval('self.' + factor_function + '(params, buy_threshold, sell_threshold)')
                        
                        if best_indicator == 'sharpe':
                            
                            # 对因子进行回测
                            signal_backtest = self.backtest(signal)
                            # 计算夏普比率
                            sharpe_ratio = self.calculate_metrics(signal_backtest).loc[0,'sharpe_ratio']
                            # 记录当前参数组合的结果
                            sharpe_records.append({
                                'code': code,
                                'factor': factor_name,
                                'param_1': param_1,
                                'param_2': param_2,
                                'sharpe_ratio': sharpe_ratio
                            })
                        elif best_indicator == 'n_return':

                            # 计算n日后收益率
                            signal_returns = self.calculate_signal_returns(signal)
                            try:
                                if best_direction == 'max':
                                    n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].max()
                                elif best_direction == 'min':
                                    n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].min()
                                elif best_direction == 'avg':
                                    n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].mean()
                                elif best_direction == 'median':
                                    n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].median()

                                # 记录当前参数组合的结果
                                sharpe_records.append({
                                    'code': code,
                                    'factor': factor_name,
                                    'param_1': param_1,
                                    'param_2': param_2,
                                    'n_after_returns': n_after_returns
                                })
                            except:
                                print(f'资产{code}因子{factor_name}参数{params}计算失败')
                                print(f"{len(signal_returns)}条数据")
                        
                # 恢复原始价格数据
                self.price = original_price

        # 将所有参数组合的结果转换为DataFrame
        sharpe_records_df = pd.DataFrame(sharpe_records)

        return sharpe_records_df

    def find_best_params_for_single_factor(self, parameters_df, rf_rate=0.02, best_indicator='sharpe', best_direction='max'):
        '''
        对于部分资产寻找部分因子的最佳参数

        :param parameters_list: 参数列表，包括标的代码、因子名称、两个可选参数列表
        :param rf_rate: 无风险利率
        :param best_indicator: 优化指标，'sharpe'表示夏普比率，'n_return'表示n日后收益率
        :param best_direction: n日后收益率聚合方式，'max'表示最大值，'min'表示最小值，'avg'表示平均值，'median'表示中位数
        :return: 最佳参数列表
        '''
        parameters_list = parameters_df.to_dict(orient='records')
        # 创建记录所有参数组合夏普比率的列表
        sharpe_records = []
        for row in parameters_list:
            factor_name = row['factor']
            code = row['code']
            params_list_1 = row['params_list_1']
            params_list_2 = row['params_list_2']

            # 获取当前资产的数据
            code_price = self.price[self.price['code'] == code]

            # 临时替换self.price为当前code的数据（用于因子计算）
            original_price = self.price
            self.price = code_price

            for param_1 in params_list_1:
                for param_2 in params_list_2:
                    signal = pd.DataFrame(code_price[['code']].reset_index())
                    if factor_name in ['SMA', 'EMA', 'KAMA', 'ADX', 'ADXTA', 'TRIX','TRIXTA', 'OBV', 'MAAMT']:
                        # # 跳过短期参数大于等于长期参数的情况
                        # if short_period >= long_period:
                        #     continue
                        factor_function = 'calculate_' + factor_name
                        
                        # 计算短线和长线指标
                        param_1_ = [param_1]
                        param_2_ = [param_2]
                        if factor_name in ['ADX', 'ADXTA']:
                            param_1_ = [param_1, 1]
                            param_2_ = [param_1, 2]
                        elif factor_name in ['MAAMT']:
                            param_1_ = [param_1, 1]
                            param_2_ = [param_1, 2]
                        elif factor_name in ['TRIX','TRIXTA']:
                            param_1_ = [param_1, param_2, 1]
                            param_2_ = [param_1, param_2, 2]
                        short_line = eval('self.' + factor_function + '(param_1_)')
                        long_line = eval('self.' + factor_function + '(param_2_)')

                        signal[factor_name+'_signal'] = short_cross_long(short_line, long_line)

                    elif factor_name in ['MACD','MACDTA', 'DPO', 'MOM', 'BIAS', 'RSI', 'RSITA', 'ROC', 'CCI','CCITA', 'CMO', 'CMOTA', 'UO', 'UOTA', 'UDVD', 'EOM', 'FI']:
                        factor_function = 'calculate_' + factor_name
                        # 计算指标值
                        if factor_name in ['MACD','MACDTA', 'DPO', 'MOM', 'ROC', 'CMO', 'CMOTA', 'UO', 'UOTA', 'EOM', 'FI']:
                            # 寻优参数不包括阈值
                            if factor_name in ['MACD','MACDTA']:
                                params = [param_1, param_2, 9]
                            elif factor_name in ['UO', 'UOTA']:
                                params = [param_1, param_1*2, param_2]
                            else:
                                params = [param_1]
                            buy_threshold = self._get_default_threshold(factor_name, 'buy')
                            sell_threshold = self._get_default_threshold(factor_name, 'sell')
                        elif factor_name in ['BIAS', 'RSI', 'RSITA', 'CCI', 'CCITA', 'UDVD']:
                            # 寻优参数包括阈值
                            params = [param_1]
                            if factor_name in ['BIAS', 'CCI', 'CCITA', 'UDVD']:
                                buy_threshold = param_2
                                sell_threshold = -param_2
                            elif factor_name in ['RSI', 'RSITA']:
                                buy_threshold = param_2
                                sell_threshold = 100 - param_2
                        indicator = eval('self.' + factor_function + '(params)')
                        # 计算买卖信号
                        signal[factor_name+'_signal'] = up_cross_threshold_buy(indicator, buy_threshold, sell_threshold)
                    elif factor_name in ['ATR', 'ATRTA', 'BOLL', 'DC', 'ACCBANDS']:
                        factor_function = 'calculate_' + factor_name
                        # 计算上下轨
                        if factor_name in ['ATR', 'ATRTA', 'BOLL']:
                            params = [param_1, param_2]
                        elif factor_name in ['DC', 'ACCBANDS']:
                            params = [param_1]
                        # 计算买卖信号
                        upper, lower = eval('self.' + factor_function + '(params)')
                        close = self.price['close'].reset_index(drop=True)
                        # 计算买卖信号
                        signal[factor_name+'_signal'] = cross_upper_lower(close, upper, lower)
                    elif factor_name in ['AROON', 'KDJ']:
                        factor_function = 'get_' + factor_name + '_signal'
                        if factor_name in ['AROON']:
                            params = [param_1]
                            buy_threshold = param_2
                            sell_threshold = param_2
                        elif factor_name in ['KDJ']:
                            params = [param_1, round(param_1/3, 0)]
                            sell_threshold = param_2
                            buy_threshold = 100 - param_2
                        # 计算买卖信号
                        params = [param_1, param_2]
                        buy_threshold = self._get_default_threshold(factor_name, 'buy')
                        sell_threshold = self._get_default_threshold(factor_name, 'sell')
                        # 计算买卖信号
                        signal[factor_name+'_signal'] = eval('self.' + factor_function + '(params, buy_threshold, sell_threshold)')
                    if best_indicator == 'sharpe':
                        
                        # 对因子进行回测
                        signal_backtest = self.backtest(signal)
                        # 计算夏普比率
                        sharpe_ratio = self.calculate_metrics(signal_backtest).loc[0,'sharpe_ratio']
                        # 记录当前参数组合的结果
                        sharpe_records.append({
                            'code': code,
                            'factor': factor_name,
                            'param_1': param_1,
                            'param_2': param_2,
                            'sharpe_ratio': sharpe_ratio
                        })
                    elif best_indicator == 'n_return':

                        # 计算n日后收益率
                        signal_returns = self.calculate_signal_returns(signal)
                        try:
                            if best_direction == 'max':
                                n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].max()
                            elif best_direction == 'min':
                                n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].min()
                            elif best_direction == 'avg':
                                n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].mean()
                            elif best_direction == 'median':
                                n_after_returns = signal_returns[(signal_returns['factor']==factor_name) & (signal_returns['direction']=='buy')]['avg_return'].median()

                            # 记录当前参数组合的结果
                            sharpe_records.append({
                                'code': code,
                                'factor': factor_name,
                                'param_1': param_1,
                                'param_2': param_2,
                                'n_after_returns': n_after_returns
                            })
                        except:
                            print(f'资产{code}因子{factor_name}参数{params}计算失败')
                            print(f"{len(signal_returns)}条数据")
                    
            # 恢复原始价格数据
            self.price = original_price
        # 将所有参数组合的结果转换为DataFrame
        sharpe_records_df = pd.DataFrame(sharpe_records)

        return sharpe_records_df
    
    def calculate_SMA(self, parameter = [5]):
        '''
        计算简单移动平均线(SMA)因子

        :param parameter: 一个包含SMA参数的列表，例如[5]表示计算5日SMA
        '''
        n = parameter[0]
        sma = self.price.groupby('code')['close'].rolling(n, min_periods=1).mean().reset_index()
        return sma['close']
        # self.factor_list = pd.merge(self.factor_list, sma, on=['code', 'date'], how='left')
        # self.factor_list = self.factor_list.rename(columns={'close': 'SMA_'+str(n)})
    
    def calculate_EMA(self, parameter = [10]):
        '''
        计算指数移动平均线(EMA)因子
        
        :param parameter: 一个包含EMA参数的列表，例如[5]表示计算5日EMA
        '''
        window = parameter[0]
        # ewm方法用于计算指数加权移动平均，span参数定义时间窗口，adjust=False表示不调整初始观察值
        ema = self.price.groupby('code')['close'].apply(
            lambda x: ema_ewm(x, window)
        ).reset_index()
        return ema['close']
        # self.factor_list = pd.merge(self.factor_list, ema, on=['code', 'date'], how='left')
        # self.factor_list = self.factor_list.rename(columns={'close': 'EMA_'+str(window)})

    def calculate_HMA(self, parameter = [5]):
        '''
        计算高价平均线(HMA)因子

        :param parameter: 一个包含HMA参数的列表，例如[5]表示计算5日HMA
        '''
        n = parameter[0]
        hma = self.price.groupby('code')['high'].rolling(n, min_periods=1).mean().reset_index()
        return hma['high']
        # self.factor_list = pd.merge(self.factor_list, hma, on=['code', 'date'], how='left')
        # self.factor_list = self.factor_list.rename(columns={'high': 'HMA_'+str(n)})

    def calculate_LMA(self, parameter = [5]):
        '''
        计算低价平均线(LMA)因子

        :param parameter: 一个包含LMA参数的列表，例如[5]表示计算5日LMA
        '''
        n = parameter[0]
        lma = self.price.groupby('code')['low'].rolling(n, min_periods=1).mean().reset_index()
        return lma['low']
        # self.factor_list = pd.merge(self.factor_list, lma, on=['code', 'date'], how='left')
        # self.factor_list = self.factor_list.rename(columns={'low': 'LMA_'+str(n)})

    def calculate_VMA(self, parameter = [5]):
        '''
        计算变异平均线(VMA)因子，VMA可以自适应改变移动平均线的周期

        计算步骤：
        1. 计算区间日度收盘价涨跌幅
        2. 计算收盘价涨跌幅的标准差
        3. 计算VMA的自适应周期: VMA Period = Round(2 * N / (1 + Standard Deviation))
        4. 使用自适应周期计算VMA值

        :param parameter: 一个包含VMA参数的列表，例如[5]表示计算5日VMA
        '''
        n = parameter[0]
        def vma_calculation(group):
            # 1. 计算日度收盘价涨跌幅
            daily_returns = group['close'].pct_change()
            
            # 2. 计算收盘价涨跌幅的标准差
            rolling_std = daily_returns.rolling(window=n, min_periods=1).std()
            
            # 3. 计算VMA的自适应周期
            # 使用fillna(1)避免初始值为NaN导致的计算错误
            vma_period = (2 * n / (1 + rolling_std.fillna(1))).round()
            
            # 确保周期至少为1
            vma_period = vma_period.clip(lower=1)
            # 4. 计算每个时点对应周期的VMA值
            vma_values = pd.Series(index=group.index)
            
            # 对每个时点计算相应周期的移动平均
            for i in range(len(group)):
                if i == 0:
                    vma_values.iloc[i] = group['close'].iloc[i]
                else:
                    period = int(vma_period.iloc[i])
                    start_idx = max(0, i - period + 1)
                    vma_values.iloc[i] = group['close'].iloc[start_idx:i+1].mean()
            
            return pd.DataFrame({'VMA_'+str(n): vma_values})
        
        # 对每个证券分组计算VMA
        vma = (
            self.price.groupby('code', group_keys=True)
            .apply(vma_calculation, include_groups=False)
            .reset_index()
        )
        return vma['VMA_'+str(n)]
        # self.factor_list = pd.merge(self.factor_list, vma, on=['code', 'date'], how='left')

    def calculate_AMV(self, parameter = [5]):
        '''
        计算成本价均线(AMV)因子

        :param parameter: 一个包含AMV参数的列表，例如[5]表示计算5日AMV
        '''
        n = parameter[0]
        # 计算成交量中间价加权平均
        def amv_calculation(group):
            numerator = ((group['open']+group['close'])/2 * group['volume']).rolling(n, min_periods=1).sum()
            denominator = group['volume'].rolling(n, min_periods=1).sum()
            return pd.DataFrame({'AMV_'+str(n): numerator / denominator})
        
        amv = (
            self.price.groupby('code', group_keys=True)
            .apply(amv_calculation, include_groups=False)
            .reset_index()
        )
        return amv['AMV_'+str(n)]
        # self.factor_list = pd.merge(self.factor_list, amv, on=['code', 'date'], how='left')
    
    def calculate_RSI(self, parameter = [14], plot = 0, buy_threshold = 30, sell_threshold = 70, start_time = '20250101', end_time = '20251231'):
        '''
        计算相对强弱指标(RSI)

        :param parameter: 包含[N]的列表，例如[14]表示14日RSI
        :param plot: 是否绘制RSI指标图
        :param buy_threshold: 买入阈值
        :param sell_threshold: 卖出阈值
        :param start_time: 开始时间
        :param end_time: 结束时间
        '''
        n = parameter[0]
        
        def rsi_calculation(group):
            close = group['close']
            delta = close.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            # 指数平滑更新
            gain = ema_ewm(gain, n)
            loss = ema_ewm(loss, n)
                        
            rsi = gain / (gain + loss) * 100
            rsi.iloc[0] = 50  # 处理初始NaN值
            return pd.DataFrame({'RSI_'+str(n):rsi})

        # 分组计算RSI
        rsi = (
            self.price.groupby('code', group_keys=True)
            .apply(rsi_calculation, include_groups=False)
            .reset_index()
        )
        
        if plot == 1:
            indicator = rsi['RSI_'+str(n)]
            signal = pd.Series(up_cross_threshold_buy(indicator, buy_threshold, sell_threshold), name = indicator.name)
            up = pd.DataFrame([buy_threshold]*len(indicator), columns=['buy_threshold'])
            down = pd.DataFrame([sell_threshold]*len(indicator), columns=['sell_threshold'])

            self.plot_factor(indicator = indicator, up = up, down = down, signals=signal, start_time=start_time, end_time=end_time)

        return rsi['RSI_'+str(n)]
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     rsi,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_RSITA(self, parameter = [14]):
        '''
        使用ta-lib提供的方法计算相对强弱指标(RSI)

        :param parameter: 包含[N]的列表，例如[14]表示14日RSI
        '''
        n = parameter[0]
        
        def rsi_calculation(group):
            rsi = ta.RSI(group['close'], timeperiod=n)
            return pd.DataFrame({'RSITA_'+str(n):rsi})

        # 分组计算RSI
        rsi = (
            self.price.groupby('code', group_keys=True)
            .apply(rsi_calculation, include_groups=False)
            .reset_index()
        )
        
        return rsi['RSITA_'+str(n)]

    def calculate_MACD(self, parameters=[12, 26, 9]):
        '''
        计算MACD指标

        :param parameters: 列表[short, long, mid]，默认[12,26,9]
        short: 短期EMA的周期，快线
        long: 长期EMA的周期，慢线
        mid: DIF与DEA的平滑周期
        '''
        short, long, mid = parameters

        def macd_calculation(group):
            close = group['close']
            # 计算EMA12与EMA26
            ema_short = ema_ewm(close, short)
            ema_long = ema_ewm(close, long)
            dif = ema_short - ema_long
            dea = ema_ewm(dif, mid)
            histogram = (dif - dea) * 2

            return pd.DataFrame({'DIF': dif, 'DEA': dea, 'MACD': histogram})

        # 分组计算MACD
        macd_data = (
            self.price.groupby('code', group_keys=True)
            .apply(macd_calculation, include_groups=False)
            .reset_index()
        )
        return macd_data['MACD']
        # 合并结果到factor_list
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     macd_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_MACDTA(self, parameters=[12, 26, 9]):
        '''
        使用ta-lib提供的方法计算MACD指标
        
        :param parameters: 列表[short, long, mid]，默认[12,26,9]
        short: 短期EMA的周期，快线
        long: 长期EMA的周期，慢线
        mid: DIF与DEA的平滑周期
        '''
        short, long, mid = parameters

        def macd_calculation(group):
            close = group['close']

            # 使用ta计算
            dif = ta.MACD(close, fastperiod=short, slowperiod=long, signalperiod=mid)[0]
            dea = ta.MACD(close, fastperiod=short, slowperiod=long, signalperiod=mid)[1]
            histogram = ta.MACD(close, fastperiod=short, slowperiod=long, signalperiod=mid)[2]
            return pd.DataFrame({'DIFTA': dif, 'DEATA': dea, 'MACDTA': histogram})

        # 分组计算MACD
        macd_data = (
            self.price.groupby('code', group_keys=True)
            .apply(macd_calculation, include_groups=False)
            .reset_index()
        )
        return macd_data['MACDTA']

    def get_KDJ_signal(self, parameters=[9, 3], buy_threshold=20, sell_threshold=80):
        '''
        计算KDJ的买卖信号

        :param parameters: 包含计算KDJ的参数的列表，例如[9，3]表示计算KDJ时的N和M值
        :param buy_threshold: 买入阈值，默认20，通常认为D值小于20处于超卖状态
        :param sell_threshold: 卖出阈值，默认80，通常认为D值大于80处于超买状态
        :return: 包含买卖信号的series
        '''
        kdj = self.calculate_KDJ(parameters)
        k = kdj['K']
        d = kdj['D']

        # 计算买卖信号
        # 如果D小于20且K上穿D，则产生买入信号
        # 如果D大于80且K下穿D，则产生卖出信号
        buy_signal = (d < buy_threshold) & (k >= d) & (k.shift(1) < d.shift(1))
        sell_signal = (d > sell_threshold) & (k <= d) & (k.shift(1) > d.shift(1))

        # 初始化信号序列和持仓状态
        signals = pd.Series(0, index=kdj.index)
        position = 0  # 0表示空仓，1表示持仓

        # 遍历时间序列，根据持仓状态过滤信号
        for i in range(len(signals)):
            if buy_signal.iloc[i] and position == 0:  # 仅在空仓时生成买入信号
                signals.iloc[i] = 1
                position = 1
            elif sell_signal.iloc[i] and position == 1:  # 仅在持仓时生成卖出信号
                signals.iloc[i] = -1
                position = 0
        # # 构建信号序列
        # signals = pd.Series(0, index=kdj.index)
        # signals = signals.mask(buy_signal, 1).mask(sell_signal, -1)
        return signals
    
    def calculate_KDJ(self, parameters=[9, 3]):
        '''
        计算KDJ指标

        :param parameters: 列表[N, M]，默认[9,3]
        '''
        N, M = parameters

        def kdj_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            # 计算N日内最高价和最低价
            min_low = low.rolling(N, min_periods=1).min()
            max_high = high.rolling(N, min_periods=1).max()
            
            # 计算RSV
            rsv = (close - min_low) / (max_high - min_low).replace(0, 1e-6) * 100
            
            # 指数移动平均计算K和D
            k = ema_ewm(rsv, M)
            d = ema_ewm(k, M)
            k.iloc[0] = 50.0  # 初始值缺失
            d.iloc[0] = 50.0
            
            # 计算J值
            j = 3 * k - 2 * d
            return pd.DataFrame({'K': k, 'D': d, 'J': j})

        # 分组计算KDJ
        kdj_data = (
            self.price.groupby('code', group_keys=True)
            .apply(kdj_calculation, include_groups=False)
            .reset_index()
        )
        return kdj_data[['K', 'D', 'J']]
        # 合并结果到factor_list
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     kdj_data,
        #     on=['code', 'date'],
        #     how='left'
        # )
    
    def calculate_SKDJ(self, parameters=[9, 3]):
        '''
        计算SKDJ指标

        :param parameters: 元组[N, M]，默认[9,3]
        '''
        N, M = parameters

        def skdj_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            # 计算RSV
            min_low = low.rolling(N, min_periods=1).min()
            max_high = high.rolling(N, min_periods=1).max()
            rsv = (close - min_low) / (max_high - min_low).replace(0, 1e-6) * 100
            
            # 双重SMA平滑
            sk = rsv.rolling(M, min_periods=1).mean()  # 慢速K值
            sd = sk.rolling(M, min_periods=1).mean()    # 慢速D值
            return pd.DataFrame({'SK': sk, 'SD': sd})

        # 显式分组并保留分组列（group_keys=True）
        skdj_data = (
            self.price.groupby('code', group_keys=True)
            .apply(skdj_calculation, include_groups=False)
            .reset_index()
        )
        return skdj_data['SK']
    
    def calculate_ALLIGAT(self, parameters=[13, 8, 5]):
        '''
        计算鳄鱼线指标

        :param parameters: 包含三个参数的列表，例如[13, 8, 5]分别表示Jaw, Teeth, Lips的周期
        '''
        jaw_period, teeth_period, lips_period = parameters
        jaw_shift, teeth_shift, lips_shift = teeth_period, lips_period, min(lips_period//2+1,lips_period-1)

        def alligator_calculation(group):
            close = group['close']
            # 计算SMA并位移
            jaw = close.rolling(jaw_period, min_periods=1).mean().shift(jaw_shift)
            teeth = close.rolling(teeth_period, min_periods=1).mean().shift(teeth_shift)
            lips = close.rolling(lips_period, min_periods=1).mean().shift(lips_shift)
            return pd.DataFrame({
                'Jaw': jaw,
                'Teeth': teeth,
                'Lips': lips
            })

        # 分组计算（保留分组列）
        alligator_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(alligator_calculation)
            .reset_index()
        )
        return alligator_data[['Jaw', 'Teeth', 'Lips']]
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     alligator_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_BBI(self, periods=[3, 6, 12, 24]):
        '''
        计算BBI指标

        :param periods: 均线周期列表，默认[3,6,12,24]
        '''
        def bbi_calculation(group):
            # 仅接收group中的close列（优化内存）
            close = group['close']
            
            # 计算各周期SMA
            sma_values = []
            for p in periods:
                sma = close.rolling(p, min_periods=1).mean()
                sma_values.append(sma)
            
            # 合并SMA并计算BBI
            sma_df = pd.concat(sma_values, axis=1)
            sma_df.columns = [f'SMA_{p}' for p in periods]
            bbi = sma_df.mean(axis=1)
            return pd.DataFrame({'BBI': bbi})

        # 分组计算
        bbi_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(bbi_calculation)
            .reset_index()
        )
        return bbi_data['BBI']
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     bbi_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_BBIBOLL(self, parameters=[11, 6]):
        '''
        计算多空布林线（BBIBOLL）指标

        :param parameters: 包含两个参数的列表，例如[11,6]分别表示标准差周期M和倍数K
        '''
        M, k = parameters
        bbi_periods = [3, 6, 12, 24]  # 固定BBI均线周期

        def bbiboll_calculation(group):
            close = group['close']
            # 计算BBI
            sma_values = []
            for p in bbi_periods:
                sma = close.rolling(p, min_periods=1).mean()
                sma_values.append(sma)
            sma_df = pd.concat(sma_values, axis=1)
            bbi = sma_df.mean(axis=1)
            # 计算布林通道
            std = bbi.rolling(M, min_periods=1).std(ddof=0)
            upper = bbi + k * std
            lower = bbi - k * std
            return pd.DataFrame({
                'BBIBOLL_Mid': bbi,
                'BBIBOLL_Upper': upper,
                'BBIBOLL_Lower': lower
            })

        # 分组计算（仅传递close列）
        bbiboll_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(bbiboll_calculation)
            .reset_index()
        )
        return bbiboll_data[['BBIBOLL_Mid', 'BBIBOLL_Upper', 'BBIBOLL_Lower']]
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     bbiboll_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_DPO(self, parameters=[20]):
        '''
        计算DPO指标

        :param parameters: 包含一个参数的列表，例如[20]表示周期N
        '''
        N = parameters[0]
        lag = N // 2 + 1  # 滞后天数

        def dpo_calculation(group):
            close = group['close']
            # 计算N日移动平均
            ma = close.rolling(N, min_periods=1).mean()
            # 滞后处理：将MA序列向前平移lag天
            lagged_ma = ma.shift(lag)
            # 计算DPO
            dpo = close - lagged_ma
            return pd.DataFrame({'DPO': dpo})

        # 分组计算（仅传递close列）
        dpo_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(dpo_calculation)
            .reset_index()
        )
        return dpo_data['DPO']
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     dpo_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_ER(self, parameters=[13]):
        '''
        计算艾达射线指标（ER）

        :param parameters: 包含一个参数的列表，例如[13]表示EMA周期
        '''
        N = parameters[0]

        def er_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            # 计算长期EMA
            ema = ema_ewm(close, N)
            # 计算Bull Power和Bear Power
            bull_power = high - ema
            bear_power = low - ema
            return pd.DataFrame({
                'Bull_Power': bull_power,
                'Bear_Power': bear_power
            })

        # 分组计算（仅传递high, low, close列）
        er_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(er_calculation)
            .reset_index()
        )
        return er_data[['Bull_Power', 'Bear_Power']]
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     er_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_TII(self, parameters=[14]):
        '''
        计算趋势强度指数（TII）

        :param parameters: 包含一个参数的列表，例如[14]表示周期N
        '''
        N = parameters[0]

        def tii_calculation(group):
            close = group['close']
            price_diff = close.diff()  # 计算每日价格变动
            # 计算上涨部分和绝对波动
            up = np.where(price_diff > 0, price_diff, 0)
            abs_diff = np.abs(price_diff)
            # 滚动窗口计算总和
            sum_up = pd.Series(up).rolling(N, min_periods=1).sum()
            sum_abs = pd.Series(abs_diff).rolling(N, min_periods=1).sum()
            # 计算TII
            tii = (sum_up / (sum_abs).replace(0,1e-6)) * 100  # +1e-6防止除零
            return pd.DataFrame({'TII': tii})

        # 分组计算（仅传递close列）
        tii_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(tii_calculation)
            .reset_index()
        )
        return tii_data['TII']
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     tii_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_PO(self, parameters=[12, 26]):
        '''
        计算价格振荡器（PO）

        :param parameters: 包含两个参数的列表，例如[12,26]表示短期与长期EMA周期
        '''
        N1, N2 = parameters[0], parameters[1]

        def po_calculation(group):
            close = group['close']
            # 计算短期EMA与长期EMA
            ema_short = ema_ewm(close, N1)
            ema_long = ema_ewm(close, N2)
            # 计算PO
            po = (ema_short - ema_long)/ (ema_long).replace(0, 1e-6) * 100  # +1e-6防止除零
            return pd.DataFrame({'PO': po})

        # 分组计算（仅传递close列）
        po_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(po_calculation)
            .reset_index()
        )
        return po_data['PO']
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     po_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_MADISPLACED(self, parameters=[20, 5]):
        '''
        计算位移移动平均线（MADisplaced）

        :param parameters: 包含两个参数的列表，例如[20,5]表示MA周期N和位移天数D
        '''
        N, D = parameters[0], parameters[1]

        def madisplaced_calculation(group):
            close = group['close']
            # 计算N日简单移动平均
            ma = close.rolling(N, min_periods=1).mean()
            # 位移处理：将MA序列向后平移D天（右移）
            displaced_ma = ma.shift(D)
            return pd.DataFrame({'MADisplaced': displaced_ma})

        # 分组计算（仅传递close列）
        mad_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(madisplaced_calculation)
            .reset_index()
        )
        return mad_data['MADisplaced']
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     mad_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_KAMA(self, parameters=[10]):
        '''
        计算考夫曼自适应移动平均线（KAMA）

        :param parameters: 包含一个参数的列表，例如[10]表示ER计算周期N
        '''
        N = parameters[0]

        def kama_calculation(group):
            close = group['close']
            
            # 1. 计算效率比率ER（使用第一个值填充空值）
            first_value = close.iloc[0]
            price_change = abs(close - close.shift(N))
            price_change = price_change.fillna(abs(close - first_value))
            
            # 计算波动率时也采用同样的处理方式
            diff = close.diff()
            diff = diff.fillna(close - first_value)
            volatility = abs(diff).rolling(N, min_periods=1).sum().replace(0, 1e-6)
            
            er = price_change / volatility

            # 2. 计算平滑系数SC（动态调整灵敏度）
            fast_sc = 2 / (2 + 1)   # 短期灵敏度参数
            slow_sc = 2 / (30 + 1)  # 长期灵敏度参数
            sc = (er * (fast_sc - slow_sc) + slow_sc).pow(2)

            # 3. 迭代计算KAMA（注意处理初始值）
            kama = close.copy()
            for i in range(1, len(kama)):
                delta = close.iloc[i] - kama.iloc[i-1]
                kama.iloc[i] = kama.iloc[i-1] + sc.iloc[i] * delta

            return pd.DataFrame({f'KAMA_{N}': kama})

        # 分组计算（仅传递close列）
        kama_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(kama_calculation)
            .reset_index()
        )
        return kama_data[f'KAMA_{N}']
    
    def get_AROON_signal(self, parameters=[20], buy_threshold=70, sell_threshold=70):
        '''
        计算阿隆指标（Aroon Up/Down）的买卖信号

        :param parameters: 包含一个参数的列表，例如[20]表示计算20日Aroon
        :param buy_threshold: 买入阈值，默认70
        :param sell_threshold: 卖出阈值，默认70
        :return: 包含买卖信号的series
        '''
        aroon_up, aroon_down = self.calculate_AROON(parameters)

        # 计算买卖信号
        buy_signal = (aroon_up >= buy_threshold) & (aroon_up.shift(1) < buy_threshold) & (aroon_down < aroon_up)
        sell_signal = (aroon_down >= sell_threshold) & (aroon_down.shift(1) < sell_threshold) & (aroon_down > aroon_up)

        # 构建信号序列
        signals = pd.Series(0, index=aroon_up.index)
        signals = signals.mask(buy_signal, 1).mask(sell_signal, -1)
        # signals = pd.Series(0, index=aroon_up.index)
        # buy_condition = (aroon_up >= 70) & (aroon_down < aroon_up)
        # signals = signals.mask(buy_condition & (buy_condition != buy_condition.shift(1)), 1)
        # sell_condition = (aroon_down >= 70) & (aroon_up < aroon_down)
        # signals = signals.mask(sell_condition & (sell_condition != sell_condition.shift(1)), -1)

        

        return signals

    def calculate_AROON(self, parameters=[20], plot = 0, buy_threshold = 70, sell_threshold = 70, start_time = '20250101', end_time = '20251231'):
        '''
        计算阿隆指标（Aroon Up/Down）

        :param parameters: 包含一个参数的列表，例如[20]表示计算20日Aroon
        :param plot: 是否绘制阿隆指标图，默认不绘制
        :param buy_threshold: 买入阈值，默认70
        :param sell_threshold: 卖出阈值，默认70
        :param start_time: 开始时间，默认为'20250101'
        :param end_time: 结束时间，默认为'20251231'
        :return: 包含Aroon_Up和Aroon_Down的元组
        '''
        N = parameters[0]

        def aroon_calculation(group):
            high = group['high']
            low = group['low']
            
            
            # 为每个窗口创建行索引数组
            def get_high_days_since(x):
                # x是窗口数据，返回当前值距离最高点的天数
                curr_idx = len(x) - 1  # 当前值的索引(最后一个)
                max_idx = x.argmax()   # 最大值的索引
                return curr_idx - max_idx  # 返回距离天数

            def get_low_days_since(x):
                # x是窗口数据，返回当前值距离最低点的天数
                curr_idx = len(x) - 1  # 当前值的索引(最后一个)
                min_idx = x.argmin()   # 最大值的索引
                return curr_idx - min_idx  # 返回距离天数

            # 计算每个窗口内的距离天数
            days_since_high = high.rolling(N+1, min_periods=1).apply(
                get_high_days_since, raw=True
            )

            days_since_low = low.rolling(N+1, min_periods=1).apply(
                get_low_days_since, raw=True
            )
            
            # 3. 计算Aroon值
            aroon_up = ((N - days_since_high) / N) * 100
            aroon_down = ((N - days_since_low) / N) * 100
            
            return pd.DataFrame({
                f'Aroon_Up_{N}': aroon_up,
                f'Aroon_Down_{N}': aroon_down,
                'Aroon_os': aroon_up - aroon_down
            })

        # 分组计算（需传递high和low列）
        aroon_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low']]
            .apply(aroon_calculation)
            .reset_index()
        )

        if plot == 1:
            # 计算AROON信号
            aroon_up, aroon_down = aroon_data['Aroon_Up_' + str(N)], aroon_data['Aroon_Down_' + str(N)]

            # 计算买卖信号
            buy_signal = (aroon_up >= buy_threshold) & (aroon_up.shift(1) < buy_threshold) & (aroon_down < aroon_up)
            sell_signal = (aroon_down >= sell_threshold) & (aroon_down.shift(1) < sell_threshold) & (aroon_down > aroon_up)

            # 构建信号序列
            signals = pd.Series(0, index=aroon_up.index)
            signals = signals.mask(buy_signal, 1).mask(sell_signal, -1)

            # 绘制Aroon指标
            indicator = pd.DataFrame({
                aroon_up.name: aroon_up,
                aroon_up.name: aroon_up
                })
            up = pd.DataFrame([buy_threshold]*len(signals), columns=['buy_threshold'])
            down = pd.DataFrame([sell_threshold]*len(signals), columns=['sell_threshold'])

            self.plot_factor(indicator, up, down, signals=signals, start_time=start_time, end_time=end_time)

        return aroon_data['Aroon_Up_' + str(N)], aroon_data['Aroon_Down_' + str(N)]

    def calculate_ADX(self, parameters=[14, 1]):
        '''
        根据定制公式计算ADX指标

        :param parameters: [N, label] N表示方向动量周期和ADX平滑周期，用label来表示短线和长线的选择
        :return: 若label为1，则返回PDI；若label为2，则返回MDI
        '''
        N, label = parameters
        
        def adx_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            prev_high = high.shift(1)
            prev_low = low.shift(1)
            
            # 计算MAX_HIGH/MAX_LOW
            max_high = (high - prev_high).where(high > prev_high, 0)
            max_low = (prev_low - low).where(prev_low > low, 0)
            
            # 计算XPDM/XNDM
            xpdm = (high - prev_high).where(max_high > max_low, 0)
            xndm = (prev_low - low).where(max_low > max_high, 0)
            
            # 使用pandas rolling求和
            pdm = xpdm.rolling(N, min_periods=1).sum()
            ndm = xndm.rolling(N, min_periods=1).sum()
            
            tr = pd.concat([
                (high - low).abs(),
                (high - close).abs(),
                (low - close).abs()
            ], axis=1).max(axis=1).rolling(N, min_periods=1).max()
            tr_sum = tr.rolling(N, min_periods=1).sum()
            
            # 4. 方向指标计算
            pdi = pdm / tr_sum.replace(0, 1e-6) * 100
            mdi = ndm / tr_sum.replace(0, 1e-6) * 100
            
            # 5. ADX生成（EMA平滑）
            dx = (abs(mdi - pdi) / (mdi + pdi).replace(0, 1e-6)) * 100
            adx = dx.ewm(span=N, adjust=False).mean()
            
            return pd.DataFrame({
                'PDI': pdi,
                'MDI': mdi,
                'ADX': adx
            })
        
        adx_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(adx_calculation)
            .reset_index()
        )

        return adx_data['PDI'] if label == 1 else adx_data['MDI']
    
    def calculate_ADXTA(self, parameters=[14, 1], plot = 0, start_time = '20250101', end_time = '20251231'):
        '''
        使用ta-lib提供的方法计算ADX指标

        :param parameters: [N, label] N表示方向动量周期和ADX平滑周期，用label来表示短线和长线的选择
        :param plot: 是否绘制指标图
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: 若label为1，则返回PDI；若label为2，则返回MDI
        '''
        N, label = parameters
        
        def adx_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']

            mdi = ta.MINUS_DI(high, low, close, N)
            pdi = ta.PLUS_DI(high, low, close, N)
            
            
            return pd.DataFrame({
                'PDITA': pdi,
                'MDITA': mdi
            })
        
        adx_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(adx_calculation)
            .reset_index()
        )

        if plot == 1:

            up = adx_data['PDITA']
            down = adx_data['MDITA']
            adxta_signal = pd.Series(short_cross_long(up, down), name = 'adxta_signal')

            self.plot_factor(up = up, down = down, signals=adxta_signal, start_time=start_time, end_time=end_time)

        return adx_data['PDITA'] if label == 1 else adx_data['MDITA']
    
    def calculate_SAR(self, parameters=[0.02, 0.2, 1]):
        '''
        计算抛物线指标（SAR）

        :param parameters: [N, M, label] 
            - N: 加速因子步长（默认0.02）
            - M: 加速因子最大值（默认0.2）
            - label：表示SAR和收盘价的选择
        :return: 若label为1，则返回收盘价序列，若label为2，则返回SAR序列
        '''
        N, M, label = parameters
        
        def sar_calculation(group):
            high = group['high'].reset_index(drop=True)
            low = group['low'].reset_index(drop=True)
            close = group['close'].reset_index(drop=True)
            
            # 初始化数组
            sar = np.full(len(close), np.nan)
            trend = np.zeros(len(close))  # 1: 上升，-1: 下降
            ep = np.full(len(close), np.nan)  # 极值点
            af = np.full(len(close), np.nan)  # 加速因子
            
            # 初始条件设置
            sar[0] = close[0]
            trend[0] = 1 if close[1] > close[0] else -1
            ep[0] = high[0] if trend[0] == 1 else low[0]
            af[0] = N
            
            for i in range(1, len(close)):
                # 趋势延续逻辑
                if trend[i-1] == 1:
                    # 计算上升趋势SAR
                    sar[i] = sar[i-1] + af[i-1] * (ep[i-1] - sar[i-1])
                    
                    # 检查趋势反转
                    if low[i] < sar[i]:
                        trend[i] = -1
                        ep[i] = low[i]
                        af[i] = N
                    else:
                        trend[i] = 1
                        ep[i] = max(high[i], ep[i-1])
                        af[i] = min(af[i-1] + N, M) if high[i] > ep[i-1] else af[i-1]
                else:
                    # 计算下降趋势SAR
                    sar[i] = sar[i-1] - af[i-1] * (sar[i-1] - ep[i-1])
                    
                    # 检查趋势反转
                    if high[i] > sar[i]:
                        trend[i] = 1
                        ep[i] = high[i]
                        af[i] = N
                    else:
                        trend[i] = -1
                        ep[i] = min(low[i], ep[i-1])
                        af[i] = min(af[i-1] + N, M) if low[i] < ep[i-1] else af[i-1]
            
            return pd.DataFrame({'SAR': sar})
        
        # 分组计算
        sar_data = (
            self.price.groupby('code', group_keys=False)[['high', 'low', 'close']]
            .apply(sar_calculation)
            .reset_index()
        )
        return sar_data['SAR'] if label == 2 else self.price.copy().reset_index()['close']

    def calculate_MOM(self, parameters=[10]):
        '''
        计算动量指标（MOM）及信号线

        :param parameters: [N] N为动量周期
        :return: 包含MOM的Series
        '''
        N = parameters[0]
        
        def mom_calculation(group):
            close = group['close']
            
            mom = close - close.shift(N)
            
            return pd.DataFrame({'MOM': mom})
        
        # 分组计算
        mom_data = (
            self.price.groupby('code', group_keys=False)[['close']]
            .apply(mom_calculation)
            .reset_index()
        )
        
        return mom_data['MOM']

    def calculate_BIAS(self, parameters=[26], plot = 0, buy_threshold = 5, sell_threshold = -5, start_time = '20250101', end_time = '20251231'):
        '''
        计算BIAS指标

        :param parameters: 周期列表，例如[26]表示BIAS6
        :param plot: 是否绘制BIAS指标图
        :param buy_threshold: 买入阈值
        :param sell_threshold: 卖出阈值
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: BIAS序列
        '''
        N = parameters[0]

        def bias_calculation(group):
            close = group['close']
            # 计算移动平均
            ma = close.rolling(N, min_periods=1).mean()
            # 计算乖离率
            bias = (close - ma) / ma * 100
            return pd.DataFrame({f'BIAS_{N}': bias})

        # 显式分组并保留分组列（group_keys=True）
        bias_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(bias_calculation)
            .reset_index()
        )

        if plot == 1:
            # 绘制BIAS指标图
            indicator = bias_data[f'BIAS_{N}']
            signal = pd.Series(up_cross_threshold_buy(indicator, buy_threshold, sell_threshold), name = indicator.name)
            up = pd.DataFrame([buy_threshold]*len(indicator), columns=['buy_threshold'])
            down = pd.DataFrame([sell_threshold]*len(indicator), columns=['sell_threshold'])
            self.plot_factor(indicator, up, down, signals=signal, start_time=start_time, end_time=end_time)

        return bias_data[f'BIAS_{N}']
        # 合并结果
        # self.factor_list = pd.merge(
        #     self.factor_list,
        #     bias_data,
        #     on=['code', 'date'],
        #     how='left'
        # )

    def calculate_ROC(self, parameters=[20], plot = 0, buy_threshold = 0, sell_threshold = 0, start_time = '20250101', end_time = '20251231'):
        '''
        计算变动率指标(ROC)

        :param parameters: 包含周期参数的列表，例如[20]表示12日ROC
        :param plot: 是否绘制ROC指标图
        :param buy_threshold: 买入阈值
        :param sell_threshold: 卖出阈值
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: ROC序列
        '''
        N = parameters[0]
        
        def roc_calculation(group):
            close = group['close']
            # 计算N天前价格
            lagged_close = close.shift(N)
            # 计算ROC
            roc = (close - lagged_close) / lagged_close.replace(0, 1e-6) * 100
            return pd.DataFrame({f'ROC_{N}': roc})
        
        # 分组计算并保持索引对齐
        roc_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(roc_calculation)
            .reset_index()
        )

        if plot == 1:
            indicator = roc_data[f'ROC_{N}']
            signal = pd.Series(up_cross_threshold_buy(indicator, buy_threshold, sell_threshold), name = indicator.name)
            up = pd.DataFrame([buy_threshold]*len(indicator), columns=['buy_threshold'])
            down = pd.DataFrame([sell_threshold]*len(indicator), columns=['sell_threshold'])

            self.plot_factor(indicator = indicator, up = up, down = down, signals=signal, start_time=start_time, end_time=end_time)

        return roc_data[f'ROC_{N}']
    
    def calculate_CCI(self, parameters=[14]):
        '''
        计算商品通道指数(CCI)

        :param parameters: 包含周期参数的列表，例如[14]表示14日CCI
        :return: CCI序列
        '''
        N = parameters[0]
        
        def cci_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            # 计算典型价格
            tp = (high + low + close) / 3
            # 计算移动平均
            matp = tp.rolling(N, min_periods=1).mean()
            # 计算平均绝对偏差
            mad = (tp - matp).abs().rolling(N, min_periods=1).mean()
            # 计算CCI（避免除零）
            cci = (tp - matp) / (0.015 * mad.replace(0, 1e-6))
            return pd.DataFrame({f'CCI_{N}': cci})
        
        # 分组计算并保持数据结构
        cci_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(cci_calculation)
            .reset_index()
        )
        return cci_data[f'CCI_{N}']
    
    def calculate_CCITA(self, parameters=[14]):
        '''
        使用ta-lib提供的方法计算商品通道指数(CCI)

        :param parameters: 包含周期参数的列表，例如[14]表示14日CCI
        :return: CCI序列
        '''
        N = parameters[0]
        
        def cci_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            cci = ta.CCI(high, low, close, timeperiod=N)
            return pd.DataFrame({f'CCITA_{N}': cci})
        
        # 分组计算并保持数据结构
        cci_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(cci_calculation)
            .reset_index()
        )
        return cci_data[f'CCITA_{N}']
    
    def calculate_CMO(self, parameters=[25]):
        '''
        计算钱德勒动量摆动指标(CMO)

        :param parameters: 包含周期参数的列表，例如[25]表示25日CMO
        :return: CMO序列
        '''
        N = parameters[0]
        
        def cmo_calculation(group):
            close = group['close']
            # 计算价格变化
            price_diff = close.diff(1)
            # 分离上涨/下跌动量
            up = price_diff.where(price_diff > 0, 0).abs()
            down = price_diff.where(price_diff < 0, 0).abs()
            # 滚动求和
            Su = up.rolling(N, min_periods=1).sum()
            Sd = down.rolling(N, min_periods=1).sum()
            # 计算CMO（防止除零）
            denominator = (Su + Sd).replace(0, 1e-6)
            cmo = (Su - Sd) / denominator * 100
            return pd.DataFrame({f'CMO_{N}': cmo})
        
        # 分组计算并保持数据结构
        cmo_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(cmo_calculation)
            .reset_index()
        )
        return cmo_data[f'CMO_{N}']
    
    def calculate_CMOTA(self, parameters=[25]):
        '''
        使用ta-lib提供的方法计算钱德勒动量摆动指标(CMO)

        :param parameters: 包含周期参数的列表，例如[25]表示25日CMO
        :return: CMO序列
        '''
        N = parameters[0]
        
        def cmo_calculation(group):
            close = group['close']
            
            cmo = ta.CMO(close, timeperiod=N)
            return pd.DataFrame({f'CMOTA_{N}': cmo})
        
        # 分组计算并保持数据结构
        cmo_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(cmo_calculation)
            .reset_index()
        )
        return cmo_data[f'CMOTA_{N}']
    
    def calculate_UO(self, parameters=[7, 14, 28]):
        '''
        计算终极振荡器(UO)

        :param parameters: 周期参数列表，默认[7,14,28]
        :return: UO序列
        '''
        n1, n2, n3 = parameters
        weights = [4, 2, 1]  # 对应三个周期的权重
        
        def uo_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            # 计算真实低点
            prior_close = close.shift(1)
            true_low = np.minimum(prior_close, low)
            # 计算真实高点
            true_high = np.maximum(prior_close, high)

            
            # 计算买入压力(BP)和真实波幅(TR)
            bp = close - true_low
            tr = true_high - true_low
            
            # 计算各周期平均值
            avg1 = bp.rolling(n1).sum() / tr.rolling(n1).sum().replace(0, 1e-6)
            avg2 = bp.rolling(n2).sum() / tr.rolling(n2).sum().replace(0, 1e-6)
            avg3 = bp.rolling(n3).sum() / tr.rolling(n3).sum().replace(0, 1e-6)
            
            # 加权合成UO
            uo = (n2*n3*avg1 + n1*n3*avg2 + n1*n3*avg3) / (n2*n3 + n1*n3 + n1*n2)  * 100
            return pd.DataFrame({'UO': uo})
        
        # 分组计算并保持数据结构
        uo_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(uo_calculation)
            .reset_index()
        )
        return uo_data['UO']
    
    def calculate_UOTA(self, parameters=[7, 14, 28], plot = 0, buy_threshold = 70, sell_threshold = 50, start_time = '20250101', end_time = '20251231'):
        '''
        计算终极振荡器(UO)

        :param parameters: 周期参数列表，默认[7,14,28]
        :return: UO序列
        '''
        n1, n2, n3 = parameters
        weights = [4, 2, 1]  # 对应三个周期的权重
        
        def uo_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            uo = ta.ULTOSC(high, low, close, timeperiod1=n1, timeperiod2=n2, timeperiod3=n3)
            return pd.DataFrame({'UOTA': uo})
        
        # 分组计算并保持数据结构
        uo_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(uo_calculation)
            .reset_index()
        )

        if plot == 1:
            # 绘制UOTA
            indicator = uo_data['UOTA']
            signal = pd.Series(up_cross_threshold_buy(indicator, buy_threshold, sell_threshold), name = indicator.name)
            up = pd.DataFrame([buy_threshold]*len(indicator), columns=['buy_threshold'])
            down = pd.DataFrame([sell_threshold]*len(indicator), columns=['sell_threshold'])
            self.plot_factor(indicator, up, down, signals=signal, start_time=start_time, end_time=end_time)

        return uo_data['UOTA']
    
    def calculate_TRIX(self, parameters=[12, 20, 1]):
        '''
        计算三重指数平滑移动平均指标(TRIX)

        :param parameters: [N, M, label] N为平滑周期，M为信号线周期，label为返回值标签
        :return: 若label为1，则返回TRIX序列，若label为2，则返回TRIXMA序列
        '''
        N, M, label = parameters
        
        def trix_calculation(group):
            close = group['close']
            
            # 三次指数平滑
            ema1 = ema_ewm(close, N)
            ema2 = ema_ewm(ema1, N)
            ema3 = ema_ewm(ema2, N)
            
            # 计算TRIX（百分比变化率）
            trix = ema3.pct_change(periods=1) * 100
            
            # 计算信号线
            signal = trix.rolling(M, min_periods=1).mean()
            return pd.DataFrame({
                f'TRIX_{N}': trix,
                f'TRIX_MA_{M}': signal
            })
        
        # 分组计算
        trix_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(trix_calculation)
            .reset_index()
        )
        return trix_data[f'TRIX_{N}'] if label == 1 else trix_data[f'TRIX_MA_{M}']
    
    def calculate_TRIXTA(self, parameters=[12, 20, 1]):
        '''
        使用ta-lib提供的方法计算三重指数平滑移动平均指标(TRIX)

        :param parameters: [N, M, label] N为平滑周期，M为信号线周期，label为返回值标签
        :return: 若label为1，则返回TRIX序列，若label为2，则返回TRIXMA序列
        '''
        N, M, label = parameters
        
        def trix_calculation(group):
            close = group['close']
            
            # 计算TRIX（百分比变化率）
            trix = ta.TRIX(close, timeperiod=N)
            
            # 计算信号线
            signal = trix.rolling(M, min_periods=1).mean()
            return pd.DataFrame({
                f'TRIXTA_{N}': trix,
                f'TRIXTA_MA_{M}': signal
            })
        
        # 分组计算
        trix_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(trix_calculation)
            .reset_index()
        )
        return trix_data[f'TRIXTA_{N}'] if label == 1 else trix_data[f'TRIXTA_MA_{M}']
    
    def calculate_ATR(self, parameters=[14, 2], plot = 0, start_time = '20250101', end_time = '20251231'):
        '''
        计算平均真实波动幅度(ATR)

        :param parameters: 包含周期参数、偏差系数的列表，例如[14，2]表示14日ATR，上下轨偏离MA2倍ATR
        :param plot: 是否绘制ATR图表
        :param start_time: 绘图开始时间
        :param end_time: 绘图结束时间
        :return: ATR序列
        '''
        N, M = parameters
        
        def atr_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            # 计算真实波幅TR
            prev_close = close.shift(1)
            tr1 = high - low
            tr2 = (high - prev_close).abs()
            tr3 = (low - prev_close).abs()
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # 计算ATR（指数移动平均）
            atr = ema_ewm(tr, N)

            # n日均值为中轨
            mid = close.rolling(N, min_periods=1).mean()
            # 计算上轨和下轨
            upper = mid + M * atr
            lower = mid - M * atr
            # 返回ATR
            return pd.DataFrame({
                'ATR': atr,
                'ATR_Upper': upper,
                'ATR_Lower': lower
            })
        
        # 分组计算并对齐索引
        atr_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(atr_calculation)
            .reset_index()
        )

        if plot == 1:

            up = atr_data['ATR_Upper']
            down = atr_data['ATR_Lower']
            close = self.price['close'].reset_index(drop=True)
            # 计算买卖信号
            signal = pd.Series(cross_upper_lower(close, up, down), name='ATR_Signal')

            self.plot_factor(up = up, down = down, signals=signal, start_time=start_time, end_time=end_time)

        return atr_data['ATR_Upper'], atr_data['ATR_Lower']
    
    def calculate_ATRTA(self, parameters=[14, 2]):
        '''
        使用ta-lib提供的方法计算平均真实波动幅度(ATR)

        :param parameters: 包含周期参数、偏差系数的列表，例如[14，2]表示14日ATR，上下轨偏离MA2倍ATR
        :return: ATR序列
        '''
        N, M = parameters
        
        def atr_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            atr = ta.ATR(high, low, close, timeperiod=N)

            # n日均值为中轨
            mid = close.rolling(N, min_periods=1).mean()
            # 计算上轨和下轨
            upper = mid + M * atr
            lower = mid - M * atr
            # 返回ATR
            return pd.DataFrame({
                'ATRTA': atr,
                'ATRTA_Upper': upper,
                'ATRTA_Lower': lower
            })
        
        # 分组计算并对齐索引
        atr_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(atr_calculation)
            .reset_index()
        )
        return atr_data['ATRTA_Upper'], atr_data['ATRTA_Lower']
    
    def calculate_BOLL(self, parameters=[20, 2]):
        '''
        计算布林带指标

        :param parameters: [N, K] N为周期，K为标准差倍数（默认[20,2]）
        :return: DataFrame包含UPPER和LOWER列
        '''
        N, K = parameters
        
        def boll_calculation(group):
            close = group['close']
            # 计算中轨
            mid = close.rolling(N, min_periods=1).mean()
            # 计算标准差
            std = close.rolling(N, min_periods=1).std(ddof=0)
            # 计算轨道
            upper = mid + K * std
            lower = mid - K * std
            return pd.DataFrame({'UPPER': upper, 'LOWER': lower})
        
        # 分组计算
        boll_data = (
            self.price.groupby('code', group_keys=True)[['close']]
            .apply(boll_calculation)
            .reset_index()
        )
        return boll_data['UPPER'], boll_data['LOWER']

    def calculate_DC(self, parameters=[20]):
        '''
        计算唐奇安通道

        :param parameters: [N] 通道周期（默认20日）
        :return: DataFrame包含UPPER和LOWER列
        '''
        N = parameters[0]
        
        def dc_calculation(group):
            high = group['high']
            low = group['low']
            
            # 计算通道轨道
            upper = high.rolling(N, min_periods=1).max()
            lower = low.rolling(N, min_periods=1).min()
            return pd.DataFrame({
                f'DC_UPPER_{N}': upper,
                f'DC_LOWER_{N}': lower,
                'DC_MID': (upper + lower) / 2
            })
        
        # 分组计算
        dc_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low']]
            .apply(dc_calculation)
            .reset_index()
        )
        return dc_data[f'DC_UPPER_{N}'], dc_data[f'DC_LOWER_{N}']
    
    def calculate_ACCBANDS(self, parameters=[20]):
        '''
        计算ACCBANDS加速带指标

        :param parameters: [N] 移动平均周期（默认20日）
        :return: DataFrame包含UPPER、LOWER、MID列
        '''
        N = parameters[0]
        
        def dynamic_bands_calculation(group):
            high = group['high']
            low = group['low']
            close = group['close']
            
            # 计算HL波动率系数（防止除零）
            denominator = (high + low).replace(0, 1e-6)
            hl_ratio = (high - low) / denominator
            
            # 调整价格计算
            adj_low = low * (1 - hl_ratio)
            adj_high = high * (1 + hl_ratio)
            
            # 计算移动平均通道
            lower = adj_low.rolling(N, min_periods=1).mean()
            upper = adj_high.rolling(N, min_periods=1).mean()
            mid = close.rolling(N, min_periods=1).mean()
            
            return pd.DataFrame({
                f'UPPER_{N}': upper,
                f'LOWER_{N}': lower,
                f'MID_{N}': mid
            })
        
        # 分组计算
        bands_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'close']]
            .apply(dynamic_bands_calculation)
            .reset_index()
        )
        return bands_data[f'UPPER_{N}'], bands_data[f'LOWER_{N}']
    
    def calculate_UDVD(self, parameters=[20], plot = 0, buy_threshold = 0, sell_threshold = 0, start_time = '20250101', end_time = '20251231'):
        '''
        计算单边波动差指标

        :param parameters: [N] 移动平均周期（默认20日）
        :return: UDVD序列
        '''
        N = parameters[0]
        
        def udvd_calculation(group):
            high = group['high']
            low = group['low']
            open_ = group['open']
            
            # 防止开盘价为0的极端情况（适用于新股）
            open_adj = open_.replace(0, 1e-6)
            
            # 计算单边波动
            vol_up = (high - open_adj) / open_adj
            vol_down = (open_adj - low) / open_adj
            
            # 计算净波动差
            net_vol = vol_up - vol_down
            udvd = net_vol.rolling(N, min_periods=1).mean()
            return pd.DataFrame({f'UDVD_{N}': udvd})
        
        # 分组计算
        udvd_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'open']]
            .apply(udvd_calculation)
            .reset_index()
        )

        if plot == 1:
            indicator = udvd_data[f'UDVD_{N}']
            signal = pd.Series(up_cross_threshold_buy(indicator, buy_threshold, sell_threshold), name = indicator.name)
            up = pd.DataFrame([buy_threshold]*len(indicator), columns=['buy_threshold'])
            down = pd.DataFrame([sell_threshold]*len(indicator), columns=['sell_threshold'])

            self.plot_factor(indicator = indicator, up = up, down = down, signals=signal, start_time=start_time, end_time=end_time)

        return udvd_data[f'UDVD_{N}']
    
    def calculate_OBV(self, parameters=[10]):
        '''
        计算能量潮指标

        :param parameters: [N] N为周期（默认10日）
        :return: OBV序列
        '''
        N = parameters[0]
        def obv_calculation(group):
            close = group['close']
            volume = group['volume']
            
            # 计算价格方向
            direction = (close.diff() > 0).astype(int) - (close.diff() < 0).astype(int)
            direction.iloc[0] = 1 if close.iloc[0] > 0 else 0  # 处理首日数据
            
            # 计算累积OBV（首日初始化为当日成交量）
            obv = (direction * volume).cumsum()

            # 计算N日移动平均
            obv_ma = obv.rolling(N, min_periods=1).mean()
            
            return pd.DataFrame({'OBV_MA': obv_ma})
        
        # 分组计算
        obv_data = (
            self.price.groupby('code', group_keys=True)[['close', 'volume']]
            .apply(obv_calculation)
            .reset_index()
        )
        return obv_data['OBV_MA']
    
    def calculate_EOM(self, parameters=[20]):
        '''
        计算简易波动指标(EOM)

        :param parameters: [N] 移动平均周期（默认20日）
        :return: EOM序列
        '''
        N = parameters[0]
        
        def eom_calculation(group):
            high = group['high']
            low = group['low']
            volume = group['volume']
            
            # 计算中间价
            mid_price = (high + low) / 2
            # 距离移动值
            distance_moved = mid_price.diff(1)
            # 箱体比率（防止除零）
            box_ratio = volume / 1e7 / (high - low).replace(0, 1e-6)
            # 计算EOM分子
            eom_raw = distance_moved / box_ratio
            # 移动平均处理
            eom = eom_raw.rolling(N, min_periods=1).mean()
            return pd.DataFrame({f'EOM_{N}': eom.fillna(0),
                                 'EMV': eom_raw.fillna(0)})
        
        # 分组计算
        eom_data = (
            self.price.groupby('code', group_keys=True)[['high', 'low', 'volume']]
            .apply(eom_calculation)
            .reset_index()
        )
        return eom_data[f'EOM_{N}']
    
    def calculate_MAAMT(self, parameters=[30, 1]):
        '''
        计算简易成交量均线

        :param parameters: [N, label] 移动平均周期（默认30日）,label表示返回值选择
        :return: 若label为1，则返回成交量序列，若label为2，则返回MAAMT序列
        '''
        N = parameters[0]
        
        def maamt_calculation(group):
            volume = group['volume']
            maamt = volume.rolling(N, min_periods=1).mean()
            return pd.DataFrame({
                f'MAAMT_{N}': maamt,
                'volume': volume
            })
        
        # 分组计算
        maamt_data = (
            self.price.groupby('code', group_keys=True)[['volume']]
            .apply(maamt_calculation)
            .reset_index()
        )
        return maamt_data[f'MAAMT_{N}'] if parameters[1] == 2 else maamt_data['volume']
    
    def calculate_FI(self, parameters=[13]):
        '''
        计算强力指数（EMA平滑版本）

        :param parameters: [N] EMA平滑周期（默认13日）
        :return: FI_EMA序列
        '''
        N = parameters[0]
        
        def fi_calculation(group):
            close = group['close']
            volume = group['volume']
            
            # 计算基础FI
            price_diff = close.diff(1)
            fi_raw = price_diff * volume
            
            # EMA平滑处理
            fi_ema = ema_ewm(fi_raw, N)
            return pd.DataFrame({f'FI_EMA_{N}': fi_ema})
        
        # 分组计算
        fi_data = (
            self.price.groupby('code', group_keys=True)[['close', 'volume']]
            .apply(fi_calculation)
            .reset_index()
        )
        return fi_data[f'FI_EMA_{N}']
    
    def backtest(self, _signal = None, mode = 1, commission=0.0003, initial_cash = 1e6):
        '''
        执行回测并计算收益率

        :param _signal: 因子信号数据，如果不传入，则使用calculate_factor方法计算出的因子信号数据，建议每次回测时都传入因子信号数据，因为类中的信号对象可能会在进行其他操作时被干扰。
        :param mode: 回测模式，默认为1，1表示信号产生第二天收盘价买入，2表示信号产生第二天开盘价买入，3表示信号产生当天收盘价买入
        :param commission: 交易手续费率（默认万三）
        :param initial_cash: 初始资金（默认100万）
        :return backtest_output: 回测结果
            键为因子名称，值为回测结果，每一个因子的回测结果为一个字典，包含以下字段：
            trade_record: 交易记录，包括日期、资产代码、交易方向、价格、数量等；
            asset_record: 资产记录，包括日期、资产代码、持仓情况、股票市值、现金、总资产等；
            net_value: 净值，索引为日期，列为资产代码，值为净值。
        ''' 
        price = self.price.reset_index()
        if _signal is None:
            signal_list = self.signal
        else:
            signal_list = _signal
        backtest_output = {}
        
        # 按资产分组回测
        grouped = price.groupby('code')
        for signal_name in signal_list.columns[2:]:
            factor = signal_name.replace('_signal', '')
            # 初始化交易记录和资产记录
            trade_record = pd.DataFrame(columns=['date', 'code', 'action', 'price', 'shares', 'total_price','win_count'])
            asset_record = pd.DataFrame(columns=['date', 'code', 'position', 'shares', 'cash', 'capital'])
            net_value = pd.DataFrame(index=price['date'].unique())
            for name, group in grouped:
                # 检查信号列是否为空
                if signal_list[signal_list['code'] == name][signal_name].isna().all():
                    continue

                position = 0
                shares = 0.0
                cash = initial_cash
                net_value[name] = 1.0  # 初始化净值为1.0
                
                for date in group['date']:
                    open_price = group[group['date'] == date]['open'].values[0]
                    close_price = group[group['date'] == date]['close'].values[0]
                    # 前一交易日
                    prev_index = group[group['date'] == date].index[0] - 1
                    if date <= group['date'].min():
                        prev_date = None
                    else:
                        prev_date = group.loc[prev_index, 'date']
                    if mode == 1:
                        # 信号产生第二天收盘价买入
                        deal_price = close_price
                        if prev_date in signal_list['date'].values:
                            signal = signal_list[(signal_list['date'] == prev_date)&(signal_list['code'] == name)][signal_name].values[0]
                        else:
                            signal = 0
                    elif mode == 2:
                        # 信号产生第二天开盘价买入
                        deal_price = open_price
                        if prev_date in signal_list['date'].values:
                            signal = signal_list[(signal_list['date'] == prev_date)&(signal_list['code'] == name)][signal_name].values[0]
                        else:
                            signal = 0
                    elif mode == 3:
                        # 信号产生当天收盘价买入
                        deal_price = close_price
                        signal = signal_list[(signal_list['date'] == date)&(signal_list['code'] == name)]['SMA_signal'].values[0]
                    
                    
                    # 买入信号（空仓时触发）
                    if signal == 1 and position == 0:
                        unit_cost = deal_price * (1 + commission)  # 扣除手续费
                        shares = cash / unit_cost  # 买入数量
                        cash = 0.0
                        position = 1
                        trade_record.loc[len(trade_record)] = [date, name, 'buy', deal_price, shares, unit_cost, 0]

                    
                    # 卖出信号（持仓时触发）
                    elif signal == -1 and position == 1:
                        unit_revenue = deal_price * (1 - commission)  # 扣除手续费
                        if unit_revenue >= unit_cost:
                            win_label = 1
                        else:
                            win_label = 0
                        trade_record.loc[len(trade_record)] = [date, name, 'sell', deal_price, shares, unit_revenue, win_label]  # 扣除手续费
                        cash = shares * unit_revenue
                        shares = 0.0
                        position = 0
                        
                    
                    # 更新资产状态
                    capital = cash + shares * close_price
                    asset_record.loc[len(asset_record)] = [date, name, position, shares, cash, capital]
                    # 更新净值
                    net_value.loc[date, name] = capital / initial_cash
            output = {
                'trade_record': trade_record,
                'asset_record': asset_record,
                'net_value': net_value
            }
            backtest_output[factor] = output
        self.backtest_output = backtest_output
        return backtest_output
    
    def calculate_signal_returns(self, signal = None, n_days=[1,3,5,10,20,30,60,120]):
        """
        计算信号产生N日后的收益表现指标

        :param signal: 信号数据，默认为None，使用self.signal
        :param n_days: N日列表
        :return: 收益表现指标结果, 包括平均收益率、中位数收益率、胜率等
        """
        results = []
        price_data = self.price.reset_index()
        if signal is not None:
            signal_data = signal.copy()
        else:
            signal_data = self.signal
        
        # 确保日期格式一致
        price_data['date'] = pd.to_datetime(price_data['date'])
        signal_data['date'] = pd.to_datetime(signal_data['date'])
        
        for signal_name in signal_data.columns[2:]:

            # 按资产代码分组处理
            for code in signal_data['code'].unique():
                # 获取当前资产的价格和信号数据
                asset_price = price_data[price_data['code'] == code].set_index('date')['close']
                asset_signal = signal_data[signal_data['code'] == code].set_index('date')
                
                # 获取所有非零信号的位置
                signal_dates = asset_signal[asset_signal[signal_name] != 0].index
                
                for n in n_days:
                    # 计算n日后收益率
                    buy_returns = []
                    sell_returns = []
                    # valid_signals = []
                    
                    for signal_date in signal_dates:
                        # 如果信号日期后n天不在价格数据范围内，则跳过
                        try:
                            future_price = asset_price.loc[signal_date:].iloc[n+1]
                        except:
                            continue
                        current_price = asset_price.loc[signal_date:].iloc[1]
                        signal_value = asset_signal.loc[signal_date, signal_name]
                        
                        # 计算n日收益率
                        ret = (future_price - current_price) / current_price
                        # 根据信号方向进行分类
                        if signal_value == 1:
                            buy_returns.append(ret)
                        elif signal_value == -1:
                            sell_returns.append(ret)
                            
                        # valid_signals.append(signal_value)
                        
                    
                    if len(buy_returns) > 0:
                        # 计算统计指标
                        avg_return = np.mean(buy_returns)
                        median_return = np.median(buy_returns)
                        win_rate = np.mean([r > 0 for r in buy_returns])
                        
                        results.append({
                            'factor': signal_name.replace('_signal', ''),
                            'code': code,
                            'direction': 'buy',
                            'n_days': n,
                            'signal_count': len(buy_returns), # 有效信号数量
                            'avg_return': avg_return * 100,  # 转换为百分比
                            'median_return': median_return * 100,  # 转换为百分比
                            'win_rate': win_rate * 100  # 转换为百分比
                        })

                    if len(sell_returns) > 0:
                        # 计算统计指标
                        avg_return = np.mean(sell_returns)
                        median_return = np.median(sell_returns)
                        win_rate = np.mean([r < 0 for r in sell_returns])
                        
                        results.append({
                            'factor': signal_name.replace('_signal', ''),
                            'code': code,
                            'direction': 'sell',
                            'n_days': n,
                            'signal_count': len(sell_returns), # 有效信号数量
                            'avg_return': avg_return * 100,  # 转换为百分比
                            'median_return': median_return * 100,  # 转换为百分比
                            'win_rate': win_rate * 100  # 转换为百分比
                        })
        
        return pd.DataFrame(results)
    
    def calculate_metrics(self, _backtest_output = None, rf_rate=0.02):
        '''
        计算所有策略评价指标

        :param _backtest_output: 回测结果，默认为None，使用self.backtest_output
        :param rf_rate: 无风险利率，默认为0.02
        :return: DataFrame，包含所有评价指标，包括夏普比率、年化收益率、年化波动率、胜率、赔率、最大回撤、换手率等
        '''
        self.rf_rate = rf_rate
        if _backtest_output is None:
            backtest_output = self.backtest_output
        else:
            backtest_output = _backtest_output
        # 初始化结果DataFrame
        results = pd.DataFrame(columns=[
            'factor', 'asset', 'sharpe_ratio', 'annualized_return', 
            'annualized_volatility', 'win_rate', 'odds_ratio', 'max_drawdown', 'turnover_rate'
        ])
        
        # 遍历每个因子的回测结果
        for key, value in backtest_output.items():
            # 计算净值指标
            net_value = value['net_value']
            net_value.index = pd.to_datetime(net_value.index)

            trade_record = value['trade_record']
            start_date = net_value.index[0]
            end_date = net_value.index[-1]
            
            # 对每个资产计算指标
            for col in net_value.columns:
                asset_value = net_value[col]
                
                # 计算胜率
                win_rate = self._calculate_win_rate(trade_record[trade_record['code'] == col])
                
                # 计算赔率
                odds_ratio = self._calculate_odds_ratio(trade_record[trade_record['code'] == col])
                
                # 计算年化指标
                metrics = self._calculate_return_metrics(asset_value)
                
                # 计算年均换仓次数
                turnover_rate = self._calculate_turnover_rate(trade_record[trade_record['code'] == col], start_date, end_date)

                # 添加结果
                results.loc[len(results)] = [
                    key,  # 因子名称
                    col,  # 资产名称
                    metrics['sharpe_ratio'],  # 夏普比率
                    metrics['annualized_return'] * 100,  # 年化收益率(%)
                    metrics['annual_std'] * 100,  # 年化波动率(%)
                    win_rate * 100,  # 胜率(%)
                    odds_ratio,  # 赔率
                    metrics['max_drawdown'] * 100,  # 最大回撤(%)
                    turnover_rate  # 年均换仓次数
                ]
        
        return results
    
    def _calculate_win_rate(self, trade_record):
        '''计算胜率'''
        win_count = trade_record[(trade_record['action'] == 'sell') & 
                               (trade_record['win_count'] == 1)]['win_count'].count()
        lose_count = trade_record[(trade_record['action'] == 'sell') & 
                                (trade_record['win_count'] == 0)]['win_count'].count()
        return win_count / (win_count + lose_count) if (win_count + lose_count) > 0 else 0
    
    def _calculate_odds_ratio(self, trade_record):
        '''计算赔率'''
        # 筛选卖出记录
        sell_records = trade_record[trade_record['action'] == 'sell']
        
        # 计算每笔交易收益率
        trades = []
        for _, sell in sell_records.iterrows():
            # 仅限每次清仓后全仓买入的情况
            buy = trade_record[(trade_record['action'] == 'buy') & 
                             (trade_record['code'] == sell['code']) &
                             (trade_record.index < sell.name)].iloc[-1]
            profit = (sell['price'] - buy['price']) / buy['price']
            trades.append(profit)
        
        trades = pd.Series(trades)
        
        # 分别计算盈利和亏损交易的平均值
        win_trades = trades[trades > 0]
        loss_trades = trades[trades < 0]
        
        if len(loss_trades) > 0:
            odds_ratio = abs(win_trades.mean() / loss_trades.mean()) if len(win_trades) > 0 else 0
        else:
            odds_ratio = float('inf') if len(win_trades) > 0 else 0
            
        return odds_ratio
    
    def _calculate_return_metrics(self, asset_value):
        '''计算收益相关指标'''
        # 计算年化收益率
        total_days = len(asset_value)
        total_return = asset_value.iloc[-1] / asset_value.iloc[0] - 1
        annualized_return = (1 + total_return) ** (252 / total_days) - 1
        
        # 计算最大回撤
        cummax = asset_value.cummax()
        drawdown = (asset_value - cummax) / cummax
        max_drawdown = drawdown.min()
        
        # 计算夏普比率
        daily_returns = asset_value.pct_change().dropna()
        annual_std = daily_returns.std() * np.sqrt(252)
        sharpe_ratio = (annualized_return - self.rf_rate) / annual_std if annual_std != 0 else np.nan
        
        return {
            'annualized_return': annualized_return,
            'max_drawdown': max_drawdown,
            'annual_std': annual_std,
            'sharpe_ratio': sharpe_ratio
        }
    
    def _calculate_turnover_rate(self, trade_record, start_date, end_date):
        '''计算年均换仓次数'''
        # 只统计卖出记录的次数作为换仓次数
        sell_trades = trade_record[trade_record['action'] == 'sell']
        total_trades = len(sell_trades)
        
        # 计算回测区间的年数
        years = (end_date - start_date).days / 365
        
        # 计算年均换仓次数
        turnover_rate = total_trades / years if years > 0 else 0
        return turnover_rate

    def plot_signals_with_price(self, signal_df, codes=None, 
                           start_time='20140101', end_time='20161231', save_dict = None):
        """
        绘制股价和信号图表
        
        :param signal_df: 信号DataFrame，包含日期、代码、多个信号列
        :param code: 股票代码，如果为None则遍历所有代码
        :param start_time: 开始时间
        :param end_time: 结束时间
        :param save_dict: 保存路径文件夹
        :return: None
        """
        price_df = self.price
        # 如果没有指定代码，则遍历所有代码
        if codes is None:
            codes = signal_df['code'].unique()
        for code in codes:
        
            # 筛选指定代码和时间范围的数据
            price_data = price_df[
                (price_df['code'] == code) & 
                (price_df.index >= start_time) & 
                (price_df.index <= end_time)
            ].copy()
            
            signal_data = signal_df[
                (signal_df['code'] == code) & 
                (signal_df['date'] >= start_time) & 
                (signal_df['date'] <= end_time)
            ].copy()
            
            # 设置日期为索引
            signal_data = signal_data.set_index('date')
            
            # 合并数据（以日期为准）
            merged_data = pd.merge(price_data[['close']], signal_data, 
                                left_index=True, right_index=True, how='left')
            
            # 创建图表
            fig, ax1 = plt.subplots(figsize=(20, 8))
            
            # 绘制股价（左轴）
            ax1.plot(merged_data.index, merged_data['close'], 
                    label='Price', color='blue', linewidth=1.5)
            ax1.set_xlabel('Date', fontsize=12)
            ax1.set_ylabel('Price', fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 创建右轴用于显示信号
            ax2 = ax1.twinx()
            
            # 获取所有信号列（排除代码列）
            signal_cols = [col for col in signal_data.columns if col != 'code']
            
            # 定义颜色和标记
            colors = ['red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan', 'magenta']
            
            # 为每个信号分配高度和样式
            signal_levels = {}
            for i, signal_col in enumerate(signal_cols):
                signal_levels[signal_col] = {
                    'level': i + 1,
                    'color': colors[i % len(colors)]
                }
            
            def find_holding_periods(signal_series):
                """
                找到持仓期间（从买入到卖出）
                返回: [(开始日期, 结束日期), ...]
                """
                holding_periods = []
                in_position = False
                buy_date = None
                
                for date, signal in signal_series.items():
                    if signal == 1 and not in_position:  # 买入信号
                        buy_date = date
                        in_position = True
                    elif signal == -1 and in_position:  # 卖出信号
                        if buy_date is not None:
                            holding_periods.append((buy_date, date))
                        in_position = False
                        buy_date = None
                
                # 如果最后还在持仓状态，延续到数据结束
                if in_position and buy_date is not None:
                    holding_periods.append((buy_date, signal_series.index[-1]))
                    
                return holding_periods
            
            # 绘制信号点和持仓期间
            for signal_col in signal_cols:
                if signal_col in merged_data.columns:
                    signal_series = merged_data[signal_col]
                    level_info = signal_levels[signal_col]
                    level = level_info['level']
                    color = level_info['color']
                    
                    # 买入信号 (1)
                    buy_mask = signal_series == 1
                    if buy_mask.any():
                        buy_dates = signal_series[buy_mask].index
                        buy_levels = [level] * len(buy_dates)
                        ax2.scatter(buy_dates, buy_levels, 
                                color=color, marker='^', 
                                s=100, alpha=0.8, 
                                label=f'{signal_col} Buy', zorder=5)
                    
                    # 卖出信号 (-1)
                    sell_mask = signal_series == -1
                    if sell_mask.any():
                        sell_dates = signal_series[sell_mask].index
                        sell_levels = [level] * len(sell_dates)
                        ax2.scatter(sell_dates, sell_levels, 
                                color=color, marker='v', 
                                s=100, alpha=0.8, 
                                label=f'{signal_col} Sell', zorder=5)
                    
                    # 绘制持仓期间的横线
                    holding_periods = find_holding_periods(signal_series)
                    for start_date, end_date in holding_periods:
                        # 在对应的高度画横线
                        ax2.plot([start_date, end_date], [level, level], 
                                color=color, linewidth=4, alpha=0.6, 
                                solid_capstyle='round', zorder=3)
                        
            # 设置右轴
            ax2.set_ylabel('Signal Types', fontsize=12)
            
            # 设置右轴的刻度和标签
            if signal_levels:
                max_level = len(signal_cols)
                ax2.set_ylim(0, max_level + 1)
                ax2.set_yticks(range(1, max_level + 1))
                ax2.set_yticklabels(signal_cols, rotation=45, ha='right')
            
            # 添加水平虚线分隔不同信号类型
            for level in range(1, len(signal_cols) + 1):
                ax2.axhline(y=level, color='lightgray', linestyle='--', alpha=0.3)
            
            # 添加图例
            ax1.legend(loc='upper left')
            # if len(signal_cols) <= 10:  # 如果信号太多，不显示图例
            #     ax2.legend(loc='upper right', bbox_to_anchor=(1.15, 1))
            
            # 设置标题
            plt.title(f'{code} - Price and Trading Signals ({start_time} to {end_time})', 
                    fontsize=14, pad=20)
            
            # 调整布局
            plt.tight_layout()
            plt.savefig(f'{save_dict}/{code} - Price and Trading Signals ({start_time} to {end_time}).png')
            # plt.show()
            plt.close()
        return

    def plot_factor(self, indicator = None, up = None, down = None, signals = None,
                    start_time = '20140101', end_time = '20161231',
                    save_plots = False, output_dir = 'factor_analysis/'):
        '''
        绘制因子信号与价格相对关系图表
        
        :param indicator: 因子数据
        :param up: 因子上界或买入阈值
        :param down: 因子下界或卖出阈值
        :param signals: 信号数据
        :param start_time: 开始时间
        :param end_time: 结束时间
        :param save_plots: 是否保存图表
        :param output_dir: 保存路径

        :return: None
        '''
        def _filter_data_by_code_and_time(price_df, signal_df, indicator_df, up_df, down_df, code, start_time, end_time):
            # 处理指标数据
            price = price_df[(price_df['code'] == code)&(price_df['date']>=start_time)&(price_df['date']<=end_time)].copy()
            indicator = indicator_df[indicator_df.index.isin(price.index)].copy()
            up = up_df[up_df.index.isin(price.index)].copy()
            down = down_df[down_df.index.isin(price.index)].copy()

            data_filtered = pd.concat([price, indicator, up, down], axis=1).set_index('date')
            data_filtered.drop(columns=['code'], inplace=True)

            # 处理信号数据
            if signal_df is not None:
                # 确保signals与筛选后的数据长度一致
                signals_filtered = signal_df[indicator_df.index.isin(price.index)].copy()
                signals_filtered.index = data_filtered.index
            return data_filtered, signals_filtered

        def _plot_single_code_factor(df_plot, signals_filtered, code, start_time, end_time, save_plots, output_dir):
            # 计算每列的均值
            column_means = df_plot.mean()

            # 创建主图和副图
            fig, ax1 = plt.subplots(figsize=(20, 6))

            # 绘制 'close' 列
            ax1.plot(df_plot.index, df_plot['close'], label='Close', color='b')
            ax1.set_xlabel('Date')
            ax1.set_ylabel('Close')
            ax1.tick_params(axis='y')
            
            # 添加买卖信号标记
            if signals_filtered is not None:
                # 找到买入信号点 (signal == 1)
                buy_mask = signals_filtered == 1
                if buy_mask.any():
                    buy_points = df_plot['close'][buy_mask]
                    ax1.scatter(buy_points.index, buy_points.values, 
                            color='red', marker='^', s=100, label='Buy Signal', zorder=5)
                
                # 找到卖出信号点 (signal == -1)
                sell_mask = signals_filtered == -1
                if sell_mask.any():
                    sell_points = df_plot['close'][sell_mask]
                    ax1.scatter(sell_points.index, sell_points.values, 
                            color='gray', marker='v', s=100, label='Sell Signal', zorder=5)
            
            
            # 创建副轴
            ax2 = ax1.twinx()

            # 遍历所有指标列，如果均值小于100，则在副轴上绘制
            for column in df_plot.columns[1:]:
                if column_means[column] < 100:
                    ax2.plot(df_plot.index, df_plot[column], label=column)
                    ax2.tick_params(axis='y')
                else:
                    ax1.plot(df_plot.index, df_plot[column], label=column)


            # 添加图例
            ax1.legend(loc='upper left')
            ax2.legend(loc='upper right')
            # 添加标题
            plt.title(f'{code} - Factor Analysis ({start_time} to {end_time})', fontsize=16, pad=20)

            # 保存图像
            if save_plots:
                filename = f'{code}_factor_analysis_{start_time}_{end_time}.png'
                plt.savefig(output_dir + filename, dpi=300, bbox_inches='tight')
            
            # 显示图像
            plt.show()
            plt.close()
            return

        df = self.price.reset_index()
        codes = df['code'].unique()
        # 为每个code单独绘图
        for code in codes:
            # 筛选当前code的数据
            code_data, code_signals = _filter_data_by_code_and_time(
                df[['date','code', 'close']], signals, indicator, up, down,
                code, start_time, end_time
            )
            # 绘制单个code的图像
            _plot_single_code_factor(
                code_data, code_signals, code, start_time, end_time, 
                save_plots, output_dir
            )
        
        return

    def merge_factors(self, signal, buy_threshold, sell_threshold, 
                 type=1, remaining=0, initial_states=None, signal_duration=10):
        """
        多因子信号合并函数，支持不同的买入和卖出阈值
        
        :param signal: 一个DataFrame，从第三列开始是信号列
        :param buy_threshold: 买入信号阈值
        :param sell_threshold: 卖出信号阈值（通常为负数）
        :param type: 合并类型
            1: 简单求和
            2: 持续状态合并（买入后保持1，卖出后保持-1）
            3: 时限状态合并（买入后保持n天1，卖出信号产生不直接归零）
            4: 信号累加合并（买入+1，卖出-1）
        :param remaining: 是否保留原始信号列（0不保留，1保留）
        :param initial_states: 字典，指定每个因子的初始状态，格式：{'factor_name': initial_state}
        :param signal_duration: type=5时信号持续的天数，默认20天
        :return: DataFrame包含合并后的信号
        """
        
        def convert_to_persistent_signal(signal_series, initial_state=0):
            """
            将单个因子的信号转换为持续状态信号（type=2使用）
            买入后保持1，卖出后保持-1
            """
            persistent_signal = []
            current_state = initial_state
            
            for sig in signal_series:
                if sig == 1:  # 买入信号
                    current_state = 1
                elif sig == -1:  # 卖出信号
                    current_state = -1
                # sig == 0 时保持当前状态
                
                persistent_signal.append(current_state)
            
            return pd.Series(persistent_signal, index=signal_series.index)
        
        def convert_to_long_only_signal(signal_series, initial_state=0):
            """
            将单个因子的信号转换为多头持续信号（type=4使用）
            买入后保持1，卖出后变为0
            """
            long_only_signal = []
            current_state = initial_state
            
            for sig in signal_series:
                if sig == 1:  # 买入信号
                    current_state = 1
                elif sig == -1:  # 卖出信号
                    current_state = 0  # 卖出后变为0，不是-1
                # sig == 0 时保持当前状态
                
                long_only_signal.append(current_state)
            
            return pd.Series(long_only_signal, index=signal_series.index)
        
        def convert_to_accumulated_signal(signal_series, initial_state=0):
            """
            将单个因子的信号转换为累加信号（type=5使用）
            买入+1，卖出-1
            """
            accumulated_signal = []
            current_state = initial_state

            for sig in signal_series:
                if sig == 1:  # 买入信号
                    current_state += 1
                elif sig == -1:  # 卖出信号
                    current_state -= 1
                # sig == 0 时保持当前状态

                accumulated_signal.append(current_state)

            return pd.Series(accumulated_signal, index=signal_series.index)

        def convert_to_time_limited_signal(signal_series, duration=20, initial_state=0):
            """
            将单个因子的信号转换为时限状态信号（type=3使用）
            买入/卖出信号产生后保持指定天数，然后自动归零
            如果期间产生新信号，重新开始计数
            """
            time_limited_signal = []
            current_state = initial_state
            remaining_days = 0  # 剩余信号天数
            
            for sig in signal_series:
                if sig == 1:  # 买入信号
                    current_state = 1
                    remaining_days = duration  # 重新开始计数
                elif sig == 0:  # 无新信号
                    if remaining_days > 0:
                        remaining_days -= 1
                        if remaining_days == 0:
                            current_state = 0  # 时间到了，归零
                    # 如果remaining_days已经是0，保持当前状态
                    # 卖出信号产生不直接归零，而是等待remaining_days结束
                
                time_limited_signal.append(current_state)
            
            return pd.Series(time_limited_signal, index=signal_series.index)
        
        
        def generate_signal_with_thresholds(total_signal, buy_thresh, sell_thresh):
            """
            根据不同的买入卖出阈值生成信号
            """
            def signal_logic(x):
                if x >= buy_thresh:
                    return 1  # 买入信号
                elif x <= sell_thresh:
                    return -1  # 卖出信号
                else:
                    return 0  # 无信号
            
            return total_signal.apply(signal_logic)
        # 初始化结果DataFrame
        outputs = []

        # 按资产代码分组处理
        for code in signal['code'].unique():
            # 获取当前资产的信号数据
            code_signal = signal[signal['code'] == code].copy()

            # 构建基础DataFrame
            df = code_signal[['date', 'code']].copy()
            signal_cols = signal.columns[2:]

            # if type == 3:
            #     # 对于每一个信号，计算最优的保持天数
            #     # 使用计算信号有效性的函数计算n日后收益率
            #     # 选择n日后日均收益率最高的周期
            #     code_signal_returns = self.calculate_signal_returns(code_signal, [1,3,5,10,20,30,60,120])
            #     code_signal_returns = code_signal_returns[code_signal_returns['direction'] == 'buy']
            #     code_signal_returns['daily_avg_return'] = code_signal_returns['avg_return'] / code_signal_returns['n_days']
            #     # 索引为('code', 'factor')元组，值为duration
            #     duration_list = code_signal_returns.groupby(['code','factor'])[['daily_avg_return', 'n_days']].apply(
            #         lambda x: x.loc[x['daily_avg_return'].idxmax(), 'n_days']
            #     ).rename('duration')

            if type == 1:
                # 简单求和方式
                total_signal = signal.iloc[:, 2:].sum(axis=1)
                df['signal_'+str(type)+'_'+str(buy_threshold)+'_'+str(sell_threshold)] = generate_signal_with_thresholds(total_signal, buy_threshold, sell_threshold)
            
            elif type in [2, 3, 4,5]:
                # 持续状态合并方式（买入后保持1，卖出后保持-1）
                # 处理初始状态
                if initial_states is None:
                    initial_states = {}
                
                # 转换每个因子为持续状态信号
                persistent_signals = {}
                
                for col in signal_cols:
                    initial_state = initial_states.get(col, 0)  # 默认初始状态为0
                    if type == 2:
                        persistent_sig = convert_to_persistent_signal(signal[col], initial_state)
                        suffix = '_persistent'
                    elif type == 3:
                        # signal_duration = duration_list.loc[(code, col.replace('_signal',''))]
                        persistent_sig = convert_to_time_limited_signal(signal[col], signal_duration, initial_state)
                        suffix = '_time_limited'
                    elif type == 4:
                        persistent_sig = convert_to_long_only_signal(signal[col], initial_state)
                        suffix = '_long_only'
                    elif type == 5:
                        persistent_sig = convert_to_accumulated_signal(signal[col], initial_state)
                        suffix = '_accumulated'
                    
                    persistent_signals[f'{col}{suffix}'] = persistent_sig
                    
                
                # 创建持续信号的DataFrame
                persistent_df = pd.DataFrame(persistent_signals, index=signal.index)
                
                # 计算综合信号
                total_persistent_signal = persistent_df.sum(axis=1)
                
                # 生成最终信号（使用不同阈值）
                # df['signal_'+str(type)+'_'+str(buy_threshold)+'_'+str(sell_threshold)] = generate_signal_with_thresholds(total_persistent_signal, buy_threshold, sell_threshold)
                df['signal_'+str(type)+'_'+str(buy_threshold)+'_'+str(sell_threshold)] = up_cross_threshold_buy(total_persistent_signal, buy_threshold, sell_threshold)
            # 处理返回的列
            if remaining == 1:
                # 保留原始信号列
                for col in signal_cols:
                    df[col] = signal[col]

            outputs.append(df)

        # 合并所有资产的结果
        result_df = pd.concat(outputs, ignore_index=True)
        result_df = result_df.sort_values(by=['date', 'code']).reset_index(drop=True)
        
        return result_df
    
