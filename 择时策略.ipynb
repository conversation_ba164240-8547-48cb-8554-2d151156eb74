from technologyindicators import *
import pymysql

config = {
    'host': '*************',
    'port': 3306,
    'user': 'ct_wind_user',
    'password': 'Ctjg2025',
    'database': 'windsh'
}
db = pymysql.connect(**config)

def sql_to_df(db, sql_query):
    cursor=db.cursor()
    cursor.execute(sql_query)
    info = cursor.fetchall()
    cols = cursor.description
    col = []
    for i in cols:
        col.append(i[0])
    data = pd.DataFrame(info,columns=col)
    return data

# 获取指数信息
index = sql_to_df(db,'select * from aindexdescription')

index[index['S_INFO_NAME'] == '中证800']

# 读取中证800量价数据
sql = '''
select * from aindexeodprices 
WHERE `S_INFO_WINDCODE` = '000906.SH' and `TRADE_DT` = '20250702'
'''
sql_to_df(db,sql)

# 读取中证800量价数据
sql = '''
select * from aindexeodprices
'''
indexprice = sql_to_df(db, sql)
# indexprice.to_csv('indexprice.csv')

indexprice.groupby('S_INFO_WINDCODE').apply(lambda x: x['TRADE_DT'].min()).rename('start_date').sort_values()

zz800_price = indexprice[indexprice['S_INFO_WINDCODE'] == '000906.SH']
zz800_price

sql = '''
select * from ashareeodprices
'''
ashareprice = sql_to_df(db, sql)

def calculate_annualized_return_from_series(price_series, periods_per_year=252):
    """
    基于价格序列计算年化收益率
    
    参数:
    price_series: 价格序列 (pandas Series 或 list)
    periods_per_year: 每年的交易周期数 (股票通常是252个交易日，月度数据是12)
    
    返回:
    年化收益率 (小数形式)
    """
    
    if isinstance(price_series, list):
        price_series = pd.Series(price_series)
    
    # 计算总收益率
    total_return = (price_series.iloc[-1] / price_series.iloc[0]) - 1
    
    # 计算持有周期数
    periods = len(price_series) - 1
    
    # 计算年化收益率
    if periods <= 0:
        raise ValueError("价格序列至少需要2个数据点")
    
    annualized_return = (1 + total_return) ** (periods_per_year / periods) - 1
    
    return annualized_return

annual_return = calculate_annualized_return_from_series(zz800['close'], periods_per_year=252)
print(f"年化收益率: {annual_return:.2%}")


tech_800 = TechnologyIndicators(zz800)
signal_800 = tech_800.calculate_factor('input_8.xlsx')
backtest_output_800 = tech_800.backtest(signal_800, mode=1, commission=0.0003)
metrics_800 = tech_800.calculate_metrics(backtest_output_800)
# metrics_800.to_csv('metrics_800.csv', index=False)
# 信号有效性检验
signal_returns_800 = tech_800.calculate_signal_returns(signal_800, [1, 3, 5, 10, 20, 30, 60, 120])
signal_returns_800.sort_values(by=['factor','direction','n_days']).to_csv('signal_returns_800.csv', index=False)

metrics_800

from codepackage.database import DatabaseManager
from codepackage.factor_scoring import FactorScoring
from codepackage.backtest import BacktestEngine
from codepackage.visualization import Visualization
from codepackage.config import StrategyConfig
from codepackage.macro_factors import MacroEconomicFactors
from codepackage.micro_factors import MicroTargetFactors
from codepackage.meso_factors import MesoMarketFactors

config = StrategyConfig()
# 初始化各个模块
db = DatabaseManager()
factor_scoring = FactorScoring(db)
backtest_engine = BacktestEngine(db)
visualization = Visualization()

MicroTargetFactors(db).calculate_value_factor()

sql = '''
select TRADE_DT from aindexvaluation
where S_INFO_WINDCODE = '000906.SH'
'''
db.sql_to_df(sql)

sql = '''
select min(TRADE_DT) from aindexeodprices
where S_INFO_WINDCODE = '000906.SH'
'''
db.sql_to_df(sql)