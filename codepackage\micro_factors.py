"""
微观标的因子计算模块
包括基本面因子（估值、动量、盈利、流动性）和技术面因子的计算
"""

import pandas as pd
import numpy as np
import talib as ta
import warnings
warnings.filterwarnings('ignore')


def mad_clip_standardize(series, multiplier=1.5):
    """
    使用MAD（中位数绝对偏差）进行截尾处理，并标准化到±1之间

    Args:
        series: 待处理的数据序列
        multiplier: MAD倍数，默认1.5倍

    Returns:
        截尾后的数据序列
    """
    # 防止修改原始数据
    series = series.copy()

    median = series.median()
    mad = np.median(np.abs(series - median))

    # 避免MAD为0的情况
    if mad == 0:
        return series

    # 计算截尾边界
    lower_bound = median - multiplier * mad
    upper_bound = median + multiplier * mad
    # 截尾
    series = np.clip(series, lower_bound, upper_bound)
    # 标准化到±1之间
    series = (series - median) / (mad * multiplier)

    return series

def ema_ewm(series, n):
    """
    使用滑动窗口计算EMA，每次取n天数据用ewm计算

    :param series: 输入的价格序列 
    :param n: EMA的周期
    :return ema_values: 包含EMA值的Series
    """
    # 初始化结果序列
    ema_values = np.zeros(len(series))
    
    # 处理第一个值
    ema_values[0] = series.iloc[0]
    
    # 循环计算每个时点的EMA
    for i in range(1, len(series)):
        if i < n:
            # 当i小于n时，使用所有可用数据
            window = series.iloc[:i+1]
        else:
            # 使用过去n天的数据
            window = series.iloc[i-n+1:i+1]
        
        # 使用ewm计算当前窗口的EMA
        # span=n表示使用n天的指数权重
        # adjust=False表示不调整初始观察值
        # min_periods=1表示至少需要1个有效值就开始计算
        ema_values[i] = window.ewm(span=n, adjust=False, min_periods=1).mean().iloc[-1]
    
    return pd.Series(ema_values, index=series.index, name=f'EMA_{n}')

class MicroTargetFactors:
    """微观标的因子计算类"""

    def __init__(self, database_manager, config=None):
        """
        初始化微观标的因子计算器

        Args:
            database_manager: 数据库管理器实例
            config: 配置对象，包含微观因子参数
        """
        self.db = database_manager
        self.config = config
    
    # def calculate_shiller_erp_factor(self, index_code='000906.SH', start_date=None, end_date=None):
    #     """
    #     计算席勒ERP因子

    #     使用过去6年通胀调整后的平均盈利计算席勒PE，通胀调整使用CPI与PPI环比均值
    #     席勒ERP=1/席勒PE-10年期国债到期收益率，1.5倍MAD截尾后标准化到±1之间

    #     Args:
    #         index_code: 指数代码
    #         start_date: 开始日期
    #         end_date: 结束日期

    #     Returns:
    #         pandas.DataFrame: 包含席勒ERP因子得分的数据
    #     """
    #     # 获取市值和盈利数据
    #     derivative_data = self.db.get_index_value_data(index_code, start_date, end_date)
        
    #     if derivative_data.empty:
    #         return pd.DataFrame()
        
    #     # 获取通胀数据
    #     inflation_data = self.db.get_macro_data(start_date, end_date)[['date', 'cpi', 'ppi']]
    #     inflation_data['inflation_rate'] = (inflation_data['cpi'] + inflation_data['ppi']) / 2
    #     inflation_data = inflation_data.set_index('date').sort_index()
    #     inflation_data = inflation_data.resample('D').ffill().reset_index()
        
    #     # 计算席勒PE
    #     data = derivative_data.copy()
    #     data = data.sort_values('date').reset_index(drop=True)
        
    #     # 计算通胀调整后的盈利
    #     data['real_earnings'] = data['operating_revenue'] / (1 + inflation_rate) ** (
    #         (data['date'] - data['date'].min()).dt.days / 365.25
    #     )

    #     # 计算过去6年滚动平均盈利（基于实际日期）
    #     data['avg_real_earnings_6y'] = data.groupby(data['date'].dt.to_period('D'))['real_earnings'].transform(
    #         lambda x: x.rolling('2190D', min_periods=1).mean()  # 6年约2190天
    #     )
        
    #     # 计算席勒PE
    #     data['shiller_pe'] = data['market_value'] / data['avg_real_earnings_6y']
        
    #     # 获取10年期国债收益率（这里简化为固定值，实际需要从数据库获取）
    #     bond_yield_10y = 0.03
        
    #     # 计算席勒ERP
    #     data['shiller_erp'] = 1 / data['shiller_pe'] - bond_yield_10y

    #     # 1.5倍MAD截尾后标准化到±1之间
    #     data['shiller_erp_clipped'] = mad_clip(data['shiller_erp'], multiplier=1.5)

    #     # 标准化到±1之间（基于MAD截尾后的范围）
    #     median_erp = data['shiller_erp'].median()
    #     mad_erp = np.median(np.abs(data['shiller_erp'] - median_erp))
    #     if mad_erp > 0:
    #         data['shiller_erp_score'] = (data['shiller_erp_clipped'] - median_erp) / (1.5 * mad_erp)
    #     else:
    #         data['shiller_erp_score'] = 0
        
    #     return data[['date', 'code', 'shiller_erp', 'shiller_erp_score']]
    
    def calculate_value_factor(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算估值因子

        PE/PB/PS×(-1)，1.5倍MAD截尾后标准化到±1之间

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含PB/PS因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            value_config = self.config.get_config('micro_factors').get('value', {})
            enabled = value_config.get('enabled', True)
            zscore_clip = value_config.get('zscore_clip', 1.5)
        else:
            enabled = True
            zscore_clip = 1.5

        # 如果因子被禁用，返回空DataFrame
        if not enabled:
            return pd.DataFrame()

        # 获取PE、PB、PS数据
        derivative_data = self.db.get_index_value_data(index_code, start_date, end_date)

        if derivative_data.empty:
            return pd.DataFrame()

        data = derivative_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算PE因子
        data['pe_factor'] = -data['pe']  # 乘以-1

        # 使用配置的截尾倍数进行MAD截尾并标准化到±1之间
        data['pe_score'] = mad_clip_standardize(data['pe_factor'], multiplier=zscore_clip)

        # 计算PB因子
        data['pb_factor'] = -data['pb']  # 乘以-1

        # 使用配置的截尾倍数进行MAD截尾并标准化到±1之间
        data['pb_score'] = mad_clip_standardize(data['pb_factor'], multiplier=zscore_clip)

        # 计算PS因子
        data['ps_factor'] = -data['ps']  # 乘以-1

        # 使用配置的截尾倍数进行MAD截尾并标准化到±1之间
        data['ps_score'] = mad_clip_standardize(data['ps_factor'], multiplier=zscore_clip)
        
        # 综合估值得分（等权平均）
        data['value_score'] = (data['pe_score'] + data['pb_score'] + data['ps_score']) / 3
        
        return data[['date', 'code', 'pe_factor', 'pb_factor', 'ps_factor', 'value_score']]
    
    def calculate_momentum_factor(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算动量因子

        过去3个月6个月12个月收益率，计算过去3个月6个月12个月收益率平均值
        1.5倍MAD截尾后标准化到±1之间得到分数

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含动量因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            momentum_config = self.config.get_config('micro_factors').get('momentum', {})
            enabled = momentum_config.get('enabled', True)
            periods = momentum_config.get('periods', [90, 180, 365])  # 默认3、6、12个月交易日
            zscore_clip = momentum_config.get('zscore_clip', 1.5)
        else:
            enabled = True
            periods = [90, 180, 365]
            zscore_clip = 1.5

        # 如果因子被禁用，返回空DataFrame
        if not enabled:
            return pd.DataFrame()

        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=max(periods) + 30)
        else:
            start_date_extended = start_date

        # 获取价格数据（使用扩展的开始日期）
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算不同期间的收益率（使用配置的期间）
        data['date'] = pd.to_datetime(data['date'])
        data = data.set_index('date').sort_index()

        # 计算各期间收益率
        returns = []
        for period in periods:
            period_days = f'{period}D'
            data[f'return_{period}d'] = data['close'].pct_change(periods=1).rolling(period_days).apply(lambda x: (x + 1).prod() - 1)
            returns.append(f'return_{period}d')

        data = data.reset_index()

        # 计算平均收益率
        data['avg_momentum'] = data[returns].mean(axis=1)

        # 使用配置的截尾倍数进行MAD截尾并标准化到±1之间
        data['momentum_score'] = mad_clip_standardize(data['avg_momentum'], multiplier=zscore_clip)

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)
        
        return data[['date', 'code', 'avg_momentum', 'momentum_score']]
    
    def calculate_roe_factor(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算ROE因子

        ROE，1.5倍MAD截尾后标准化到±1之间得到分数
        指数ROE算法：指数算法：2*∑（成份股净利润ttm）/∑（成份股净资产+一年前净资产)*100%
        说明：分子分母同成份，期初期末成份不同的，以期末成份为准，对期初添加该成份。

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含ROE因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            roe_config = self.config.get_config('micro_factors').get('roe', {})
            enabled = roe_config.get('enabled', True)
            zscore_clip = roe_config.get('zscore_clip', 1.5)
        else:
            enabled = True
            zscore_clip = 1.5

        # 如果因子被禁用，返回空DataFrame
        if not enabled:
            return pd.DataFrame()

        # ROE计算需要一年前的净资产数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=400)  # 提前约13个月
        else:
            start_date_extended = start_date

        # 使用数据库中的指数ROE计算函数（使用扩展的开始日期）
        roe_data = self.db.calculate_index_roe(index_code, start_date_extended, end_date)

        # 重命名列以保持一致性
        roe_data = roe_data.rename(columns={'index_roe': 'roe'})

        # 使用配置的截尾倍数进行MAD截尾并标准化到±1之间
        roe_data['roe_score'] = mad_clip_standardize(roe_data['roe'], multiplier=zscore_clip)

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            roe_data = roe_data[roe_data['date'] >= start_date]
        roe_data = roe_data.reset_index(drop=True)

        return roe_data[['date', 'code', 'roe', 'roe_score', 'constituent_count']]
    
    def calculate_liquidity_factor(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算流动性因子

        换手率计算方法：期间总成交量/平均流通股本
        计算过去3个月、6个月、12个月换手率的平均值
        1.5倍MAD截尾后标准化到±1之间得到分数

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含流动性因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            liquidity_config = self.config.get_config('micro_factors').get('liquidity', {})
            enabled = liquidity_config.get('enabled', True)
            periods = liquidity_config.get('periods', [90, 180, 365])  # 默认3、6、12个月交易日
            zscore_clip = liquidity_config.get('zscore_clip', 1.5)
        else:
            enabled = True
            periods = [90, 180, 365]
            zscore_clip = 1.5

        # 如果因子被禁用，返回空DataFrame
        if not enabled:
            return pd.DataFrame()

        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=max(periods) + 30)
        else:
            start_date_extended = start_date

        # 获取价格数据（使用扩展的开始日期）
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 获取流通股本数据（使用扩展的开始日期）
        value_data = self.db.get_index_value_data(index_code, start_date_extended, end_date)

        # 合并流通股本数据（使用流通股本字段）
        data = data.merge(value_data[['date', 'float_total_shares']], on='date', how='left')
        # 前向填充缺失的流通股本数据
        data['float_total_shares'] = data['float_total_shares'].fillna(method='ffill')

        # 基于实际时间计算换手率
        data['date'] = pd.to_datetime(data['date'])
        data = data.set_index('date').sort_index()

        # 计算不同期间的换手率（使用配置的期间）
        turnovers = []
        for period in periods:
            period_days = f'{period}D'
            data[f'volume_{period}d'] = data['volume'].rolling(period_days).sum()
            data[f'float_cap_{period}d'] = data['float_total_shares'].rolling(period_days).mean()
            data[f'turnover_{period}d'] = data[f'volume_{period}d'] / data[f'float_cap_{period}d']
            turnovers.append(f'turnover_{period}d')

        data = data.reset_index()

        # 计算平均流动性
        data['avg_liquidity'] = data[turnovers].mean(axis=1)

        # 使用配置的截尾倍数进行MAD截尾并标准化到±1之间
        data['liquidity_score'] = mad_clip_standardize(data['avg_liquidity'], multiplier=zscore_clip)

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'avg_liquidity', 'liquidity_score']]

    def calculate_bias_factor(self, index_code='000906.SH', period=7, threshold=4,
                            start_date=None, end_date=None):
        """
        计算BIAS因子

        价格与移动平均的百分比偏离度
        BIAS大于5时产生买入信号即分数为1，BIAS小于-5时产生卖出信号即分数为-1

        Args:
            index_code: 指数代码
            period: 移动平均周期
            threshold: 阈值
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含BIAS因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            bias_config = self.config.get_config('technical_factors').get('bias', {})
            enabled = bias_config.get('enabled', True)
            period = bias_config.get('period', period)  # 使用配置值或函数参数
            threshold = bias_config.get('threshold', threshold)  # 使用配置值或函数参数
        else:
            enabled = True

        # 如果因子被禁用，返回空DataFrame
        if not enabled:
            return pd.DataFrame()

        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period + 10)
        else:
            start_date_extended = start_date

        # 获取价格数据（使用扩展的开始日期）
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算移动平均（使用配置的周期）
        data['ma'] = data['close'].rolling(period, min_periods=1).mean()

        # 计算BIAS
        data['bias'] = (data['close'] - data['ma']) / data['ma'] * 100

        # 计算BIAS因子得分（使用配置的阈值）
        data['bias_score'] = np.where(
            data['bias'] > threshold, 1,
            np.where(data['bias'] < -threshold, -1, 0)
        )

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'bias', 'bias_score']]

    def calculate_aroon_factor(self, index_code='000906.SH', period=20, threshold=70,
                             start_date=None, end_date=None):
        """
        计算AROON因子

        AROONUp = (N-N日最高价距当前天数)/N×100
        AROONDown = (N-N日最低价距当前天数)/N×100
        AROON = AROONUp - AROONDown
        当AROONUp大于70且AROON大于0时产生买入信号
        当AROONDown大于70且AROON小于0时产生卖出信号

        Args:
            index_code: 指数代码
            period: 计算周期
            threshold: 阈值
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含AROON因子得分的数据
        """
        if self.config:
            aroon_config = self.config.get_config('technical_factors').get('aroon', {})
            enabled = aroon_config.get('enabled', True)
            period = aroon_config.get('period', period)  # 使用配置值或函数参数
            threshold = aroon_config.get('threshold', threshold)  # 使用配置值或函数参数
        else:
            enabled = True

        if not enabled:
            return pd.DataFrame()
        
        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period + 10)
        else:
            start_date_extended = start_date

        # 获取价格数据
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 使用talib计算AROON
        aroon_down, aroon_up = ta.AROON(data['high'].values, data['low'].values, timeperiod=period)

        data['aroon_up'] = aroon_up
        data['aroon_down'] = aroon_down
        data['aroon'] = data['aroon_up'] - data['aroon_down']

        # 计算AROON因子得分
        buy_condition = (data['aroon_up'] > threshold) & (data['aroon'] > 0)
        sell_condition = (data['aroon_down'] > threshold) & (data['aroon'] < 0)

        data['aroon_score'] = np.where(
            buy_condition, 1,
            np.where(sell_condition, -1, 0)
        )

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'aroon', 'aroon_score']]

    def calculate_adx_factor(self, index_code='000906.SH', period=14,
                           start_date=None, end_date=None):
        """
        计算ADX因子

        使用talib库中的函数，当DI+大于DI-时产生买入信号，当DI-大于DI+时产生卖出信号

        Args:
            index_code: 指数代码
            period: 计算周期
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含ADX因子得分的数据
        """
        if self.config:
            adx_config = self.config.get_config('technical_factors').get('adx', {})
            enabled = adx_config.get('enabled', True)
            period = adx_config.get('period', period)  # 使用配置值或函数参数
        else:
            enabled = True

        if not enabled:
            return pd.DataFrame()

        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period + 10)
        else:
            start_date_extended = start_date

        # 获取价格数据
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 使用talib计算ADX和DI
        data['adx'] = ta.ADX(data['high'].values, data['low'].values, data['close'].values, timeperiod=period)
        data['di_plus'] = ta.PLUS_DI(data['high'].values, data['low'].values, data['close'].values, timeperiod=period)
        data['di_minus'] = ta.MINUS_DI(data['high'].values, data['low'].values, data['close'].values, timeperiod=period)

        # 计算ADX因子得分
        data['adx_score'] = np.where(
            data['di_plus'] > data['di_minus'], 1,
            np.where(data['di_minus'] > data['di_plus'], -1, 0)
        )

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'adx', 'adx_score']]

    def calculate_uo_factor(self, index_code='000906.SH', period1=5, period2=10, period3=90,
                          buy_threshold=70, sell_threshold=50, start_date=None, end_date=None):
        """
        计算UO因子

        使用talib库中的函数，当UO大于70时产生买入信号，当UO小于50时产生卖出信号

        Args:
            index_code: 指数代码
            period1, period2, period3: UO计算的三个周期
            buy_threshold: 买入阈值
            sell_threshold: 卖出阈值
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含UO因子得分的数据
        """
        if self.config:
            uo_config = self.config.get_config('technical_factors').get('uo', {})
            enabled = uo_config.get('enabled', True)
            period1 = uo_config.get('period1', period1)  # 使用配置值或函数参数
            period2 = uo_config.get('period2', period2)  # 使用配置值或函数参数
            period3 = uo_config.get('period3', period3)  # 使用配置值或函数参数
            buy_threshold = uo_config.get('buy_threshold', buy_threshold)  # 使用配置值或函数参数
            sell_threshold = uo_config.get('sell_threshold', sell_threshold)  # 使用配置值或函数参数
        else:
            enabled = True
        if not enabled:
            return pd.DataFrame()

        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period3 + 10)
        else:
            start_date_extended = start_date

        # 获取价格数据
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 使用talib计算UO
        data['uo'] = ta.ULTOSC(
            data['high'].values, data['low'].values, data['close'].values,
            timeperiod1=period1, timeperiod2=period2, timeperiod3=period3
        )

        # 计算UO因子得分
        data['uo_score'] = np.where(
            data['uo'] > buy_threshold, 1,
            np.where(data['uo'] < sell_threshold, -1, 0)
        )

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'uo', 'uo_score']]

    def calculate_atr_factor(self, index_code='000906.SH', period=14, multiplier=2,
                           start_date=None, end_date=None):
        """
        计算ATR因子

        TR = MAX(ABS((HIGH-LOW), ABS(REF(CLOSE, 1)-HIGH)), ABS(REF(CLOSE,1)-LOW)) 
        ATR = EMA(TR, N) 
        MIDDLE = MA(CLOSE, N) 
        UPPER = MIDDLE + M×ATR(N) 
        LOWER = MIDDLE - M×ATR(N)
        当股价大于上轨时产生买入信号，当股价小于下轨时产生卖出信号
        上轨 = 收盘价 + multiplier * ATR
        下轨 = 收盘价 - multiplier * ATR

        Args:
            index_code: 指数代码
            period: ATR计算周期
            multiplier: ATR倍数
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含ATR因子得分的数据
        """
        if self.config:
            atr_config = self.config.get_config('technical_factors').get('atr', {})
            enabled = atr_config.get('enabled', True)
            period = atr_config.get('period', period)  # 使用配置值或函数参数
            multiplier = atr_config.get('multiplier', multiplier)  # 使用配置值或函数参数
        else:
            enabled = True
        if not enabled:
            return pd.DataFrame()
        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period + 10)
        else:
            start_date_extended = start_date
        # 获取价格数据
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算ATR
        data['tr'] = np.maximum(np.abs(data['high'] - data['low']), 
                                np.abs(data['close'].shift(1) - data['high']),
                                np.abs(data['close'].shift(1) - data['low']))
        data['atr'] = ema_ewm(data['tr'], period)


        # 计算上下轨
        data['upper_band'] = data['close'] + multiplier * data['atr']
        data['lower_band'] = data['close'] - multiplier * data['atr']

        # 计算ATR因子得分
        data['atr_score'] = np.where(
            data['close'] > data['upper_band'], 1,
            np.where(data['close'] < data['lower_band'], -1, 0)
        )

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'atr', 'atr_score']]

    def calculate_udvd_factor(self, index_code='000906.SH', period=20,
                            start_date=None, end_date=None):
        """
        计算UDVD因子

        VOLUP = (HIGH-OPEN)/OPEN
        VOLDOWN = (OPEN-LOW)/OPEN
        UDVD = SMA(VOLUP-VOLDOWN, N)
        当UDVD大于0时产生买入信号，当UDVD小于0时产生卖出信号

        Args:
            index_code: 指数代码
            period: 平滑周期
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含UDVD因子得分的数据
        """
        if self.config:
            udvd_config = self.config.get_config('technical_factors').get('udvd', {})
            enabled = udvd_config.get('enabled', True)
            period = udvd_config.get('period', period)  # 使用配置值或函数参数
        else:
            enabled = True

        if not enabled:
            return pd.DataFrame()
        # 为了确保有足够的历史数据，需要提前开始日期
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period + 10)
        else:
            start_date_extended = start_date
        # 获取价格数据
        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算VOLUP和VOLDOWN
        data['vol_up'] = (data['high'] - data['open']) / data['open']
        data['vol_down'] = (data['open'] - data['low']) / data['open']

        # 计算UDVD
        data['udvd'] = (data['vol_up'] - data['vol_down']).rolling(period, min_periods=1).mean()

        # 计算UDVD因子得分
        data['udvd_score'] = np.where(
            data['udvd'] > 0, 1,
            np.where(data['udvd'] < 0, -1, 0)
        )

        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            data = data[data['date'] >= start_date]
        data = data.reset_index(drop=True)

        return data[['date', 'code', 'udvd', 'udvd_score']]

    def calculate_new_high_low_factor(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算新高新低数因子

        计算中证800指数成分股为过去一年新高的数量，以及过去一年新低的数量
        并计算差值=新高数-新低数，对差值计算ma20平滑
        若平滑后的差值<0，则说明近期新低成分股较多，指数有见底的预期，此时发出看多信号，分数为1
        反之若平滑后的差值>0，则指数有不久后见顶的可能，此时发出看空信号，分数为-1

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含新高新低数因子得分的数据
        """
        if self.config:
            new_high_low_config = self.config.get_config('technical_factors').get('new_high_low', {})
            enabled = new_high_low_config.get('enabled', True)
            period = new_high_low_config.get('period', period)  # 使用配置值或函数参数
        else:
            enabled = True

        if not enabled:
            return pd.DataFrame()
        
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=period + 10)
        else:
            start_date_extended = start_date
        # 获取指数成分股数据
        index_data = self.db.get_index_component_data(index_code, start_date_extended, end_date)
        
        # 获取成分股价格数据
        codes = index_data['code'].unique().tolist()
        price_data = self.db.get_price_data(codes, start_date_extended, end_date)

        # 获取股票年度最高价和最低价
        high_low_data = self.db.get_stock_derivative_indicators(codes, start_date_extended, end_date)['code', 'date', 'high_52w', 'low_52w']

        # 合并数据
        data = pd.merge(index_data, price_data, on=['code', 'date'], how='left')
        data = pd.merge(data, high_low_data, on=['code', 'date'], how='left')

        # 计算新高新低数
        data['is_new_high'] = (data['high'] >= data['high_52w']).astype(int)
        data['is_new_low'] = (data['low'] <= data['low_52w']).astype(int)

        # 根据index_code统计每日新高新低数
        new_high_low_count = data.groupby(['date', 'index_code']).agg({
            'is_new_high': 'sum', 
            'is_new_low': 'sum'
        }).reset_index()

        # 计算新高新低差值
        new_high_low_count['high_low_diff'] = new_high_low_count['is_new_high'] - new_high_low_count['is_new_low']

        # MA20平滑
        new_high_low_count['high_low_diff_ma20'] = new_high_low_count['high_low_diff'].rolling(20, min_periods=10).mean()

        # 计算新高新低因子得分
        new_high_low_count['new_high_low_score'] = np.where(
            new_high_low_count['high_low_diff_ma20'] < 0, 1,  # 新低股票多，见底预期，看多
            np.where(new_high_low_count['high_low_diff_ma20'] > 0, -1, 0)  # 新高股票多，见顶预期，看空
        )
        # 如果指定了原始开始日期，则过滤结果
        if start_date:
            new_high_low_count = new_high_low_count[new_high_low_count['date'] >= start_date]
        new_high_low_count = new_high_low_count.reset_index(drop=True)

        return new_high_low_count[['date', 'index_code', 'high_low_diff_ma20', 'new_high_low_score']]

    def calculate_all_micro_factors(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算所有微观标的因子

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含所有微观标的因子得分的数据
        """
        # 计算基本面因子（根据配置的enabled参数决定是否计算）
        fundamental_factors = {}
        technical_factors = {}

        if self.config:
            micro_config = self.config.get_config('micro_factors')
            technical_config = self.config.get_config('technical_factors')

            # 基本面因子
            if micro_config.get('value', {}).get('enabled', True):
                fundamental_factors['value'] = self.calculate_value_factor(index_code, start_date, end_date)
            if micro_config.get('momentum', {}).get('enabled', True):
                fundamental_factors['momentum'] = self.calculate_momentum_factor(index_code, start_date, end_date)
            if micro_config.get('roe', {}).get('enabled', True):
                fundamental_factors['roe'] = self.calculate_roe_factor(index_code, start_date, end_date)
            if micro_config.get('liquidity', {}).get('enabled', True):
                fundamental_factors['liquidity'] = self.calculate_liquidity_factor(index_code, start_date, end_date)

            # 技术面因子
            if technical_config.get('bias', {}).get('enabled', True):
                technical_factors['bias'] = self.calculate_bias_factor(index_code, start_date=start_date, end_date=end_date)
            if technical_config.get('aroon', {}).get('enabled', True):
                technical_factors['aroon'] = self.calculate_aroon_factor(index_code, start_date=start_date, end_date=end_date)
            if technical_config.get('adx', {}).get('enabled', True):
                technical_factors['adx'] = self.calculate_adx_factor(index_code, start_date=start_date, end_date=end_date)
            if technical_config.get('uo', {}).get('enabled', True):
                technical_factors['uo'] = self.calculate_uo_factor(index_code, start_date=start_date, end_date=end_date)
            if technical_config.get('atr', {}).get('enabled', True):
                technical_factors['atr'] = self.calculate_atr_factor(index_code, start_date=start_date, end_date=end_date)
            if technical_config.get('udvd', {}).get('enabled', True):
                technical_factors['udvd'] = self.calculate_udvd_factor(index_code, start_date=start_date, end_date=end_date)
            if technical_config.get('new_high_low', {}).get('enabled', True):
                technical_factors['new_high_low'] = self.calculate_new_high_low_factor(index_code, start_date=start_date, end_date=end_date)
        else:
            # 如果没有配置，计算所有因子
            fundamental_factors['value'] = self.calculate_value_factor(index_code, start_date, end_date)
            fundamental_factors['momentum'] = self.calculate_momentum_factor(index_code, start_date, end_date)
            fundamental_factors['roe'] = self.calculate_roe_factor(index_code, start_date, end_date)
            fundamental_factors['liquidity'] = self.calculate_liquidity_factor(index_code, start_date, end_date)

            technical_factors['bias'] = self.calculate_bias_factor(index_code, start_date=start_date, end_date=end_date)
            technical_factors['aroon'] = self.calculate_aroon_factor(index_code, start_date=start_date, end_date=end_date)
            technical_factors['adx'] = self.calculate_adx_factor(index_code, start_date=start_date, end_date=end_date)
            technical_factors['uo'] = self.calculate_uo_factor(index_code, start_date=start_date, end_date=end_date)
            technical_factors['atr'] = self.calculate_atr_factor(index_code, start_date=start_date, end_date=end_date)
            technical_factors['udvd'] = self.calculate_udvd_factor(index_code, start_date=start_date, end_date=end_date)
            technical_factors['new_high_low'] = self.calculate_new_high_low_factor(index_code, start_date=start_date, end_date=end_date)

        # 创建日期范围
        all_dates = set()
        all_factors = {**fundamental_factors, **technical_factors}

        for df in all_factors.values():
            if not df.empty:
                all_dates.update(df['date'].tolist())

        if not all_dates:
            return pd.DataFrame()

        result = pd.DataFrame({'date': sorted(all_dates)})
        result['code'] = index_code

        # 合并基本面因子得分
        fundamental_score_mapping = {
            'value': 'value_score',
            'momentum': 'momentum_score',
            'roe': 'roe_score',
            'liquidity': 'liquidity_score'
        }

        for factor_name, score_col in fundamental_score_mapping.items():
            if factor_name in fundamental_factors and not fundamental_factors[factor_name].empty and score_col in fundamental_factors[factor_name].columns:
                result = result.merge(
                    fundamental_factors[factor_name][['date', score_col]],
                    on='date', how='left'
                )

        # 合并技术面因子得分
        technical_score_mapping = {
            'bias': 'bias_score',
            'aroon': 'aroon_score',
            'adx': 'adx_score',
            'uo': 'uo_score',
            'atr': 'atr_score',
            'udvd': 'udvd_score',
            'new_high_low': 'new_high_low_score'
        }

        for factor_name, score_col in technical_score_mapping.items():
            if factor_name in technical_factors and not technical_factors[factor_name].empty and score_col in technical_factors[factor_name].columns:
                result = result.merge(
                    technical_factors[factor_name][['date', score_col]],
                    on='date', how='left'
                )

        # 分别计算基本面和技术面得分
        fundamental_cols = ['value_score', 'momentum_score', 'roe_score', 'liquidity_score']
        technical_cols = ['bias_score', 'aroon_score', 'adx_score', 'uo_score', 'atr_score', 'udvd_score', 'new_high_low_score']

        # 基本面得分（等权平均）
        available_fundamental_cols = [col for col in fundamental_cols if col in result.columns]
        if available_fundamental_cols:
            result['fundamental_score'] = result[available_fundamental_cols].mean(axis=1, skipna=True)
        else:
            result['fundamental_score'] = 0

        # 技术面得分（等权平均）
        available_technical_cols = [col for col in technical_cols if col in result.columns]
        if available_technical_cols:
            result['technical_score'] = result[available_technical_cols].mean(axis=1, skipna=True)
        else:
            result['technical_score'] = 0

        # 微观标的综合得分（等权平均）
        result['micro_score'] = (result['fundamental_score'] + result['technical_score']) / 2

        return result

    def get_factor_description(self):
        """
        获取因子描述信息

        Returns:
            dict: 因子描述字典
        """
        return {
            'shiller_erp': {
                'name': '席勒ERP因子',
                'description': '使用过去6年通胀调整后的平均盈利计算席勒PE，席勒ERP=1/席勒PE-10年期国债到期收益率',
                'data_source': 'AShareEODDerivativeIndicator表中的s_val_mv、oper_rev_ttm字段'
            },
            'pb_ps': {
                'name': 'PB/PS因子',
                'description': 'PB/PS×(-1)，1.5倍MAD截尾后标准化到±1之间',
                'data_source': 'AShareEODDerivativeIndicator表中的s_val_pb_new，s_val_ps_ttm字段'
            },
            'momentum': {
                'name': '动量因子',
                'description': '过去3个月6个月12个月收益率平均值，1.5倍MAD截尾后标准化',
                'data_source': '指数价格数据'
            },
            'roe': {
                'name': 'ROE因子',
                'description': '指数ROE算法：2*∑（成份股净利润ttm）/∑（成份股净资产+一年前净资产)*100%，1.5倍MAD截尾后标准化到±1之间',
                'data_source': '成分股净利润TTM和净资产数据，通过database.calculate_index_roe计算'
            },
            'liquidity': {
                'name': '流动性因子',
                'description': '换手率=期间总成交量/平均流通股本，计算过去3个月6个月12个月换手率平均值，1.5倍MAD截尾后标准化',
                'data_source': '成交量数据和database.get_index_value_data获取的流通股本数据(float_total_shares字段)'
            },
            'bias': {
                'name': 'BIAS因子',
                'description': '价格与移动平均的百分比偏离度，BIAS大于5时买入，小于-5时卖出',
                'data_source': '指数价格数据'
            },
            'aroon': {
                'name': 'AROON因子',
                'description': 'AROON指标，当AROONUp>70且AROON>0时买入，当AROONDown>70且AROON<0时卖出',
                'data_source': '指数价格数据'
            },
            'adx': {
                'name': 'ADX因子',
                'description': 'ADX指标，当DI+大于DI-时买入，当DI-大于DI+时卖出',
                'data_source': '指数价格数据'
            },
            'uo': {
                'name': 'UO因子',
                'description': 'Ultimate Oscillator，当UO>70时买入，当UO<50时卖出',
                'data_source': '指数价格数据'
            },
            'atr': {
                'name': 'ATR因子',
                'description': 'Average True Range，当股价大于上轨时买入，小于下轨时卖出',
                'data_source': '指数价格数据'
            },
            'udvd': {
                'name': 'UDVD因子',
                'description': 'Up Down Volume Difference，当UDVD>0时买入，<0时卖出',
                'data_source': '指数价格数据'
            },
            'new_high_low': {
                'name': '新高新低数因子',
                'description': '计算成分股新高新低数量差值，平滑后<0看多，>0看空',
                'data_source': '指数成分股价格数据'
            }
        }
