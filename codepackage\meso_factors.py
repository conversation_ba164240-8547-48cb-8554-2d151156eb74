"""
中观市场因子计算模块
包括资金面因子和市场情绪因子的计算
已check
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')


class MesoMarketFactors:
    """中观市场因子计算类"""

    def __init__(self, database_manager, config=None):
        """
        初始化中观市场因子计算器

        Args:
            database_manager: 数据库管理器实例
            config: 配置对象，包含中观因子参数
        """
        self.db = database_manager
        self.config = config
    
    def calculate_margin_trading_factor(self, start_date=None, end_date=None):
        """
        计算两融增量因子

        计算融资余额-融券余额，并计算其过去120天均增量与过去240天均增量
        若120日均增量>过去240日均增量，则说明当前杠杆资金上行，此时看多，分数为1
        反之看空，分数为-1

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含两融增量因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            margin_trading_config = self.config.get_config('meso_factors').get('margin_trading', {})
            short_window = margin_trading_config.get('short_window', 120)
            long_window = margin_trading_config.get('long_window', 240)
        else:
            short_window = 120
            long_window = 240

        # 获取融资融券数据
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=long_window)
        else:
            start_date_extended = start_date
        margin_data = self.db.get_margin_trading_data(start_date_extended, end_date)
        margin_data = margin_data.groupby('date').agg(
            {'financing_balance': 'sum',
            'securities_lending_balance': 'sum'}
        ).sort_values('date').reset_index()

        if margin_data.empty:
            return pd.DataFrame()

        data = margin_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算融资余额-融券余额
        data['net_margin_balance'] = (
            data['financing_balance'] - data['securities_lending_balance']
        )

        # 基于实际时间计算过去短期和长期窗口均增量（使用配置的窗口大小）
        data['date'] = pd.to_datetime(data['date'])
        data = data.set_index('date').sort_index()

        data['net_balance_short_avg_increment'] = (
            data['net_margin_balance'].rolling(short_window).mean().diff()
        )

        data['net_balance_long_avg_increment'] = (
            data['net_margin_balance'].rolling(long_window).mean().diff()
        )

        # 两融增量因子得分
        data['margin_trading_score'] = np.where(
            data['net_balance_short_avg_increment'] > data['net_balance_long_avg_increment'],
            1,  # 杠杆资金上行，看多
            -1  # 杠杆资金下行，看空
        )
        
        if start_date:
            data = data[data['date'] >= start_date]

        data = data.reset_index(drop=True)
        return data[['date', 'net_margin_balance', 'margin_trading_score']]
    
    def calculate_turnover_trend_factor(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算成交额趋势因子

        对数成交额，并计算其均线距离=ma120/ma240-1
        均线距离的max(10)=max(30)=max(60)时看多，分数为1
        当均线距离的min(10)=min(30)=min(60)时看空，分数为-1

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含成交额趋势因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            turnover_config = self.config.get_config('meso_factors').get('turnover_trend', {})
            ma_short = turnover_config.get('ma_short', 120)
            ma_long = turnover_config.get('ma_long', 240)
            max_min_windows = turnover_config.get('max_min_windows', [10, 30, 60])
        else:
            ma_short = 120
            ma_long = 240
            max_min_windows = [10, 30, 60]

        # 获取指数价格数据（包含成交额）
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=ma_long)
        else:
            start_date_extended = start_date

        price_data = self.db.get_index_price_data(index_code, start_date_extended, end_date)

        if price_data.empty:
            return pd.DataFrame()

        data = price_data.copy()
        data = data.sort_values('date').reset_index(drop=True)

        # 计算对数成交额
        data['log_amount'] = np.log(data['amount'] + 1)  # 加1避免log(0)

        # 基于实际时间计算短期和长期移动平均（使用配置的窗口大小）
        data['date'] = pd.to_datetime(data['date'])
        data = data.set_index('date').sort_index()

        data['ma_short'] = data['log_amount'].rolling(ma_short).mean()
        data['ma_long'] = data['log_amount'].rolling(ma_long).mean()

        # 计算均线距离
        data['ma_distance'] = data['ma_short'] / data['ma_long'] - 1

        # 计算不同周期的最大值和最小值（使用配置的窗口）
        data['ma_distance_max_10'] = data['ma_distance'].rolling(f'{max_min_windows[0]}D').max()
        data['ma_distance_max_30'] = data['ma_distance'].rolling(f'{max_min_windows[1]}D').max()
        data['ma_distance_max_60'] = data['ma_distance'].rolling(f'{max_min_windows[2]}D').max()

        data['ma_distance_min_10'] = data['ma_distance'].rolling(f'{max_min_windows[0]}D').min()
        data['ma_distance_min_30'] = data['ma_distance'].rolling(f'{max_min_windows[1]}D').min()
        data['ma_distance_min_60'] = data['ma_distance'].rolling(f'{max_min_windows[2]}D').min()
        
        # 成交额趋势因子得分
        data['turnover_trend_score'] = 0
        
        # 当均线距离的max(10)=max(30)=max(60)时看多
        max_condition = (
            (data['ma_distance_max_10'] == data['ma_distance_max_30']) &
            (data['ma_distance_max_30'] == data['ma_distance_max_60'])
        )
        
        # 当均线距离的min(10)=min(30)=min(60)时看空
        min_condition = (
            (data['ma_distance_min_10'] == data['ma_distance_min_30']) &
            (data['ma_distance_min_30'] == data['ma_distance_min_60'])
        )
        
        data.loc[max_condition, 'turnover_trend_score'] = 1
        data.loc[min_condition, 'turnover_trend_score'] = -1
        
        if start_date:
            data = data[data['date'] >= start_date]

        data = data.reset_index(drop=True)
        return data[['date', 'code', 'ma_distance', 'turnover_trend_score']]
    
    def calculate_market_money_flow_factor(self, start_date=None, end_date=None):
        """
        计算市场资金净流入因子

        计算过去120天均资金净流入与过去240天均资金净流入
        若120日均资金净流入>240日均资金净流入，则说明当前市场资金净流入上行，此时看多，分数为1
        反之看空，分数为-1

        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 包含市场资金净流入因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            market_money_flow_config = self.config.get_config('meso_factors').get('market_money_flow', {})
            short_window = market_money_flow_config.get('short_window', 120)
            long_window = market_money_flow_config.get('long_window', 240)
        else:
            short_window = 120
            long_window = 240

        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=long_window)
        else:
            start_date_extended = start_date

        # 获取市场资金净流入数据
        money_flow_data = self.db.get_money_flow_data(start_date_extended, end_date)[['date', 'net_inflow']]
        money_flow_data = money_flow_data.groupby('date').agg('sum').sort_values('date').reset_index()

        if money_flow_data.empty:
            return pd.DataFrame()

        # 基于实际时间计算过去短期和长期窗口均资金净流入（使用配置的窗口大小）
        money_flow_data['date'] = pd.to_datetime(money_flow_data['date'])
        money_flow_data = money_flow_data.set_index('date').sort_index()

        money_flow_data['net_flow_short_avg'] = money_flow_data['net_inflow'].rolling(short_window).mean()
        money_flow_data['net_flow_long_avg'] = money_flow_data['net_inflow'].rolling(long_window).mean()

        # 市场资金净流入因子得分
        money_flow_data['market_money_flow_score'] = np.where(
            money_flow_data['net_flow_short_avg'] > money_flow_data['net_flow_long_avg'],
            1,  # 资金净流入上行，看多
            -1  # 资金净流入下行，看空
        )
        
        if start_date:
            money_flow_data = money_flow_data[money_flow_data['date'] >= start_date]

        money_flow_data = money_flow_data.reset_index(drop=True)
        return money_flow_data[['date', 'net_inflow', 'market_money_flow_score']]
    
    def calculate_main_money_flow_factor(self, start_date=None, end_date=None):
        """
        计算主力资金流向因子
        
        主力资金一般指大单和超大单资金，主力资金流向即为大单与超大单的净流入量
        若120日均主力资金净流入>240日均主力资金净流入，则说明当前主力资金净流入上行，此时看多，分数为1
        反之看空，分数为-1
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 包含主力资金流向因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            main_money_flow_config = self.config.get_config('meso_factors').get('main_money_flow', {})
            short_window = main_money_flow_config.get('short_window', 120)
            long_window = main_money_flow_config.get('long_window', 240)
        else:
            short_window = 120
            long_window = 240

        # 获取主力资金净流入数据
        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(days=long_window)
        else:
            start_date_extended = start_date

        main_money_flow = self.db.get_money_flow_data(start_date_extended, end_date)[['date', 'buy_value_exlarge', 'sell_value_exlarge', 'buy_value_large', 'sell_value_large']]
        main_money_flow = main_money_flow.groupby('date').agg('sum').sort_values('date').reset_index()

        # 计算主力资金净流入
        main_money_flow['main_money_flow'] = main_money_flow['buy_value_exlarge'] + main_money_flow['buy_value_large']
        - main_money_flow['sell_value_exlarge'] - main_money_flow['sell_value_large']

        # 基于实际时间计算过去短期和长期窗口均主力资金净流入（使用配置的窗口大小）
        main_money_flow['date'] = pd.to_datetime(main_money_flow['date'])
        main_money_flow = main_money_flow.set_index('date').sort_index()

        main_money_flow['main_money_flow_short_avg'] = main_money_flow['main_money_flow'].rolling(short_window).mean()
        main_money_flow['main_money_flow_long_avg'] = main_money_flow['main_money_flow'].rolling(long_window).mean()
        
        # 主力资金流向因子得分
        main_money_flow['main_money_flow_score'] = np.where(
            main_money_flow['main_money_flow_short_avg'] > main_money_flow['main_money_flow_long_avg'],
            1,  # 主力资金净流入上行，看多
            -1  # 主力资金净流入下行，看空
        )
        
        if start_date:
            main_money_flow = main_money_flow[main_money_flow['date'] >= start_date]

        main_money_flow = main_money_flow.reset_index(drop=True)
        return main_money_flow[['date', 'main_money_flow', 'main_money_flow_score']]
    
    def calculate_vix_factor(self, start_date=None, end_date=None):
        """
        计算恐慌指数VIX因子
        
        VIX指数越高，表明市场恐慌情绪越浓，市场波动预期越大
        计算过去三个月VIX指数变化，若VIX指数变化>0，则说明VIX指数上升，此时看空，分数为-1
        若VIX指数变化<0，则说明VIX指数下降，此时看多，分数为1
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 包含VIX因子得分的数据
        """
        # 从配置中获取参数，如果没有配置则使用默认值
        if self.config:
            vix_config = self.config.get_config('meso_factors').get('vix', {})
            lookback_months = vix_config.get('lookback_months', 3)
        else:
            lookback_months = 3

        if start_date:
            start_date_extended = pd.to_datetime(start_date) - pd.DateOffset(months=lookback_months)
        else:
            start_date_extended = start_date

        vix_data = self.db.get_macro_data(start_date_extended, end_date)[['date', 'vix']]

        # 计算回看月数前的VIX指数（使用配置的回看月数）
        lookback_days = lookback_months * 30  # 近似转换为天数
        vix_data['vix_lookback'] = vix_data['vix'].shift(lookback_days)

        # 计算VIX指数变化
        vix_data['vix_change'] = vix_data['vix'] - vix_data['vix_lookback']

        # VIX因子得分
        vix_data['vix_score'] = np.where(
            vix_data['vix_change'] > 0, -1,  # VIX指数上升，看跌情绪增加，看空
            np.where(vix_data['vix_change'] < 0, 1, 0)  # VIX指数下降，看跌情绪减少，看多
        )

        if start_date:
            vix_data = vix_data[vix_data['date'] >= start_date]

        vix_data = vix_data.reset_index(drop=True)
        return vix_data[['date', 'vix_change', 'vix_score']]
    

    def calculate_all_meso_factors(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算所有中观市场因子

        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 包含所有中观市场因子得分的数据
        """
        # 计算各个因子（根据配置的enabled参数决定是否计算）
        factors = {}

        # 检查配置并计算启用的因子
        if self.config:
            meso_config = self.config.get_config('meso_factors')

            if meso_config.get('margin_trading', {}).get('enabled', True):
                factors['margin_trading'] = self.calculate_margin_trading_factor(start_date, end_date)
            if meso_config.get('turnover_trend', {}).get('enabled', True):
                factors['turnover_trend'] = self.calculate_turnover_trend_factor(index_code, start_date, end_date)
            if meso_config.get('market_money_flow', {}).get('enabled', True):
                factors['market_money_flow'] = self.calculate_market_money_flow_factor(start_date, end_date)
            if meso_config.get('main_money_flow', {}).get('enabled', True):
                factors['main_money_flow'] = self.calculate_main_money_flow_factor(start_date, end_date)
            if meso_config.get('vix', {}).get('enabled', True):
                factors['vix'] = self.calculate_vix_factor(start_date, end_date)
        else:
            # 如果没有配置，计算所有因子
            factors['margin_trading'] = self.calculate_margin_trading_factor(start_date, end_date)
            factors['turnover_trend'] = self.calculate_turnover_trend_factor(index_code, start_date, end_date)
            factors['market_money_flow'] = self.calculate_market_money_flow_factor(start_date, end_date)
            factors['main_money_flow'] = self.calculate_main_money_flow_factor(start_date, end_date)
            factors['vix'] = self.calculate_vix_factor(start_date, end_date)

        # 创建日期范围
        all_dates = set()
        for df in factors.values():
            if not df.empty:
                all_dates.update(df['date'].tolist())

        if not all_dates:
            return pd.DataFrame()

        result = pd.DataFrame({'date': sorted(all_dates)})

        # 添加指数代码
        result['code'] = index_code

        # 合并各个因子得分
        factor_score_mapping = {
            'margin_trading': 'margin_trading_score',
            'turnover_trend': 'turnover_trend_score',
            'market_money_flow': 'market_money_flow_score',
            'main_money_flow': 'main_money_flow_score',
            'vix': 'vix_score'
        }

        for factor_name, score_col in factor_score_mapping.items():
            if factor_name in factors and not factors[factor_name].empty and score_col in factors[factor_name].columns:
                result = result.merge(
                    factors[factor_name][['date', score_col]],
                    on='date', how='left'
                )

        # 分别计算资金面和市场情绪得分
        capital_flow_cols = ['margin_trading_score', 'turnover_trend_score',
                           'market_money_flow_score', 'main_money_flow_score']
        market_sentiment_cols = ['vix_score']

        # 资金面得分（等权平均）
        available_capital_cols = [col for col in capital_flow_cols if col in result.columns]
        if available_capital_cols:
            result['capital_flow_score'] = result[available_capital_cols].mean(axis=1, skipna=True)
        else:
            result['capital_flow_score'] = 0

        # 市场情绪得分（等权平均）
        available_sentiment_cols = [col for col in market_sentiment_cols if col in result.columns]
        if available_sentiment_cols:
            result['market_sentiment_score'] = result[available_sentiment_cols].mean(axis=1, skipna=True)
        else:
            result['market_sentiment_score'] = 0

        # 中观市场综合得分（等权平均）
        result['meso_score'] = (result['capital_flow_score'] + result['market_sentiment_score']) / 2

        return result

    def get_factor_description(self):
        """
        获取因子描述信息

        Returns:
            dict: 因子描述字典
        """
        return {
            'margin_trading': {
                'name': '两融增量因子',
                'description': '计算融资余额-融券余额，比较过去120天均增量与过去240天均增量',
                'data_source': 'AShareMarginTradeSum表中的s_marsum_tradingbalance和s_marsum_seclendingbalance'
            },
            'turnover_trend': {
                'name': '成交额趋势因子',
                'description': '对数成交额的均线距离=ma120/ma240-1，通过不同周期最值比较判断趋势',
                'data_source': '指数行情数据中的成交额字段'
            },
            'market_money_flow': {
                'name': '市场资金净流入因子',
                'description': '比较过去120天均资金净流入与过去240天均资金净流入',
                'data_source': 'AShareMoneyFlow表中的s_moneyflow_netamount字段'
            },
            'main_money_flow': {
                'name': '主力资金流向因子',
                'description': 'L2净流入，比较过去120天均L2净流入与过去240天均L2净流入',
                'data_source': 'L2资金流向数据'
            },
            'vix': {
                'name': 'VIX恐慌指数因子',
                'description': '过去三个月VIX指数变化，VIX上升看空，下降看多',
                'data_source': 'VIX恐慌指数数据'
            },
            'pcr': {
                'name': 'PCR认沽认购比率因子',
                'description': '过去三个月PCR变化，PCR上升看空，下降看多',
                'data_source': '期权认沽认购比率数据'
            }
        }
