"""
结果可视化和指标计算模块
实现净值曲线绘制、年化收益率、波动率、回撤、夏普比率等指标计算
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('ggplot')


class Visualization:
    """可视化和指标计算类"""
    
    def __init__(self):
        """初始化可视化类"""
        self.figure_size = (12, 8)
        self.colors = {
            'strategy': '#1f77b4',
            'benchmark': '#ff7f0e',
            'drawdown': '#d62728',
            'positive': '#2ca02c',
            'negative': '#d62728'
        }
    
    def plot_nav_comparison(self, backtest_results, benchmark_data, title="策略与基准净值对比"):
        """
        绘制策略与基准的净值曲线对比图
        
        Args:
            backtest_results: 回测结果DataFrame
            benchmark_data: 基准数据DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 合并数据
        if not backtest_results.empty and not benchmark_data.empty:
            merged_data = backtest_results[['date', 'nav']].merge(
                benchmark_data[['date', 'benchmark_nav']], 
                on='date', how='inner'
            )
            
            # 绘制净值曲线
            ax.plot(merged_data['date'], merged_data['nav'], 
                   color=self.colors['strategy'], linewidth=2, label='策略净值')
            ax.plot(merged_data['date'], merged_data['benchmark_nav'], 
                   color=self.colors['benchmark'], linewidth=2, label='基准净值')
            
            # 设置图表格式
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('净值', fontsize=12)
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.xticks(rotation=45)
            
            # 添加统计信息
            strategy_return = (merged_data['nav'].iloc[-1] - 1) * 100
            benchmark_return = (merged_data['benchmark_nav'].iloc[-1] - 1) * 100
            excess_return = strategy_return - benchmark_return
            
            info_text = f'策略收益: {strategy_return:.2f}%\n基准收益: {benchmark_return:.2f}%\n超额收益: {excess_return:.2f}%'
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_drawdown(self, backtest_results, title="策略回撤分析"):
        """
        绘制回撤曲线
        
        Args:
            backtest_results: 回测结果DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figure_size, height_ratios=[2, 1])
        
        if not backtest_results.empty:
            # 计算回撤
            nav_series = backtest_results['nav']
            rolling_max = nav_series.expanding().max()
            drawdown = (nav_series - rolling_max) / rolling_max * 100
            
            # 绘制净值曲线
            ax1.plot(backtest_results['date'], nav_series, 
                    color=self.colors['strategy'], linewidth=2, label='策略净值')
            ax1.fill_between(backtest_results['date'], nav_series, rolling_max, 
                           where=(nav_series < rolling_max), color=self.colors['drawdown'], 
                           alpha=0.3, label='回撤区域')
            
            ax1.set_title(title, fontsize=16, fontweight='bold')
            ax1.set_ylabel('净值', fontsize=12)
            ax1.legend(fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 绘制回撤曲线
            ax2.fill_between(backtest_results['date'], drawdown, 0, 
                           color=self.colors['drawdown'], alpha=0.7)
            ax2.plot(backtest_results['date'], drawdown, 
                    color=self.colors['drawdown'], linewidth=1)
            
            ax2.set_xlabel('日期', fontsize=12)
            ax2.set_ylabel('回撤 (%)', fontsize=12)
            ax2.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            for ax in [ax1, ax2]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            
            plt.xticks(rotation=45)
            
            # 添加最大回撤信息
            max_drawdown = drawdown.min()
            max_dd_date = backtest_results.loc[drawdown.idxmin(), 'date']
            
            info_text = f'最大回撤: {max_drawdown:.2f}%\n发生日期: {max_dd_date.strftime("%Y-%m-%d")}'
            ax2.text(0.02, 0.02, info_text, transform=ax2.transAxes, 
                    verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_monthly_returns(self, backtest_results, title="月度收益率分布"):
        """
        绘制月度收益率分布
        
        Args:
            backtest_results: 回测结果DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figure_size)
        
        if not backtest_results.empty:
            # 计算月度收益率
            monthly_data = backtest_results.copy()
            monthly_data['date'] = pd.to_datetime(monthly_data['date'])
            monthly_data['year_month'] = monthly_data['date'].dt.to_period('M')
            
            monthly_returns = monthly_data.groupby('year_month')['nav'].last().pct_change().dropna() * 100
            
            # 绘制月度收益率柱状图
            colors = [self.colors['positive'] if x > 0 else self.colors['negative'] for x in monthly_returns]
            ax1.bar(range(len(monthly_returns)), monthly_returns, color=colors, alpha=0.7)
            ax1.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
            ax1.set_title('月度收益率', fontsize=14, fontweight='bold')
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('收益率 (%)', fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 设置x轴标签
            if len(monthly_returns) > 12:
                step = len(monthly_returns) // 12
                ax1.set_xticks(range(0, len(monthly_returns), step))
                ax1.set_xticklabels([str(monthly_returns.index[i]) for i in range(0, len(monthly_returns), step)], 
                                  rotation=45)
            else:
                ax1.set_xticks(range(len(monthly_returns)))
                ax1.set_xticklabels([str(x) for x in monthly_returns.index], rotation=45)
            
            # 绘制月度收益率分布直方图
            ax2.hist(monthly_returns, bins=20, color=self.colors['strategy'], alpha=0.7, edgecolor='black')
            ax2.axvline(x=monthly_returns.mean(), color='red', linestyle='--', linewidth=2, label=f'均值: {monthly_returns.mean():.2f}%')
            ax2.set_title('月度收益率分布', fontsize=14, fontweight='bold')
            ax2.set_xlabel('收益率 (%)', fontsize=12)
            ax2.set_ylabel('频次', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 添加统计信息
            positive_months = (monthly_returns > 0).sum()
            total_months = len(monthly_returns)
            win_rate = positive_months / total_months * 100
            
            stats_text = f'正收益月份: {positive_months}/{total_months}\n胜率: {win_rate:.1f}%\n平均收益: {monthly_returns.mean():.2f}%\n收益标准差: {monthly_returns.std():.2f}%'
            ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_factor_scores(self, factor_scores, title="因子得分时序图"):
        """
        绘制因子得分时序图
        
        Args:
            factor_scores: 因子得分DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        if not factor_scores.empty:
            score_cols = ['macro_score', 'meso_score', 'micro_score', 'comprehensive_score']
            score_names = ['宏观经济得分', '中观市场得分', '微观标的得分', '综合因子得分']
            
            for i, (col, name) in enumerate(zip(score_cols, score_names)):
                if col in factor_scores.columns:
                    ax = axes[i]
                    ax.plot(factor_scores['date'], factor_scores[col], 
                           linewidth=1.5, label=name)
                    ax.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
                    ax.fill_between(factor_scores['date'], factor_scores[col], 0, 
                                  where=(factor_scores[col] > 0), color=self.colors['positive'], alpha=0.3)
                    ax.fill_between(factor_scores['date'], factor_scores[col], 0, 
                                  where=(factor_scores[col] < 0), color=self.colors['negative'], alpha=0.3)
                    
                    ax.set_title(name, fontsize=12, fontweight='bold')
                    ax.set_ylabel('得分', fontsize=10)
                    ax.grid(True, alpha=0.3)
                    
                    # 格式化x轴
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def create_performance_report(self, backtest_summary, benchmark_summary=None):
        """
        创建业绩报告表格
        
        Args:
            backtest_summary: 策略回测汇总
            benchmark_summary: 基准回测汇总
            
        Returns:
            pandas.DataFrame: 业绩报告表格
        """
        metrics = [
            ('总收益率', 'total_return', '{:.2%}'),
            ('年化收益率', 'annualized_return', '{:.2%}'),
            ('年化波动率', 'annualized_volatility', '{:.2%}'),
            ('夏普比率', 'sharpe_ratio', '{:.3f}'),
            ('最大回撤', 'max_drawdown', '{:.2%}'),
            ('胜率', 'win_rate', '{:.2%}'),
            ('盈亏比', 'profit_loss_ratio', '{:.3f}'),
            ('交易成本占比', 'transaction_cost_ratio', '{:.2%}'),
            ('交易天数', 'trading_days', '{:.0f}')
        ]
        
        report_data = []
        
        for metric_name, metric_key, format_str in metrics:
            row = {'指标': metric_name}
            
            if metric_key in backtest_summary:
                row['策略'] = format_str.format(backtest_summary[metric_key])
            else:
                row['策略'] = 'N/A'
            
            if benchmark_summary and metric_key in benchmark_summary:
                row['基准'] = format_str.format(benchmark_summary[metric_key])
                
                # 计算超额
                if metric_key in ['total_return', 'annualized_return']:
                    excess = backtest_summary[metric_key] - benchmark_summary[metric_key]
                    row['超额'] = format_str.format(excess)
                else:
                    row['超额'] = '-'
            else:
                row['基准'] = 'N/A'
                row['超额'] = '-'
            
            report_data.append(row)
        
        return pd.DataFrame(report_data)
    
    def save_all_charts(self, backtest_results, benchmark_data, factor_scores, 
                       output_dir='./charts/', prefix='strategy'):
        """
        保存所有图表
        
        Args:
            backtest_results: 回测结果
            benchmark_data: 基准数据
            factor_scores: 因子得分
            output_dir: 输出目录
            prefix: 文件名前缀
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # 净值对比图
        fig1 = self.plot_nav_comparison(backtest_results, benchmark_data)
        fig1.savefig(f'{output_dir}/{prefix}_nav_comparison.png', dpi=300, bbox_inches='tight')
        plt.close(fig1)
        
        # 回撤分析图
        fig2 = self.plot_drawdown(backtest_results)
        fig2.savefig(f'{output_dir}/{prefix}_drawdown.png', dpi=300, bbox_inches='tight')
        plt.close(fig2)
        
        # 月度收益率图
        fig3 = self.plot_monthly_returns(backtest_results)
        fig3.savefig(f'{output_dir}/{prefix}_monthly_returns.png', dpi=300, bbox_inches='tight')
        plt.close(fig3)
        
        # 因子得分图
        if not factor_scores.empty:
            fig4 = self.plot_factor_scores(factor_scores)
            fig4.savefig(f'{output_dir}/{prefix}_factor_scores.png', dpi=300, bbox_inches='tight')
            plt.close(fig4)
        
        print(f"所有图表已保存到 {output_dir} 目录")
