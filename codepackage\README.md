# 中证800择时策略系统

## 项目简介

本项目是一个完整的中证800指数择时策略系统，基于三层架构的多维度因子分析，包括宏观（经济）、中观（市场）、微观（标的）三大层次因子，通过综合评分进行择时决策。

## 系统架构

### 核心模块

1. **数据库模块** (`database.py`)
   - 连接Wind数据库
   - 获取指数价格、衍生指标、利率、融资融券等数据

2. **因子计算模块**
   - `macro_factors.py`: 宏观经济因子（生产、消费、投资、货币、汇率、国债、信用）
   - `meso_factors.py`: 中观市场因子（资金面、市场情绪）
   - `micro_factors.py`: 微观标的因子（基本面、技术面）

3. **评分系统** (`factor_scoring.py`)
   - 因子标准化处理
   - 综合评分计算
   - 交易信号生成

4. **回测引擎** (`backtest.py`)
   - 策略回测
   - 业绩指标计算
   - 信号有效性分析

5. **可视化模块** (`visualization.py`)
   - 净值曲线图
   - 回撤分析图
   - 因子得分图
   - 业绩报告

6. **配置系统** (`config.py`)
   - 参数配置管理
   - 配置文件读写
   - 参数验证

7. **主程序** (`main.py`)
   - 策略主类
   - 完整流程控制

## 安装和配置

### 环境要求

- Python 3.7+
- Wind数据库访问权限

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据库配置

在配置文件中设置数据库连接参数：

```json
{
    "database": {
        "host": "*************",
        "port": 3306,
        "user": "ct_windsh",
        "password": "Ctzqsh262700!",
        "database": "windsh"
    }
}
```

## 使用方法

### 基本使用

```python
from main import CSI800TimingStrategy

# 创建策略实例
strategy = CSI800TimingStrategy()

# 运行策略
results = strategy.run_strategy()

# 关闭连接
strategy.close()
```

### 自定义配置

```python
from config import StrategyConfig
from main import CSI800TimingStrategy

# 创建自定义配置
config = StrategyConfig()
config.set_config('backtest', 'start_date', '2022-01-01')
config.set_config('backtest', 'end_date', '2023-12-31')

# 修改因子权重
custom_weights = {
    'valuation_score': 0.3,
    'fundamental_score': 0.3,
    'capital_score': 0.2,
    'technical_score': 0.2
}
config.config['factor_weights'] = custom_weights

# 保存配置
config.save_config('custom_config.json')

# 使用自定义配置
strategy = CSI800TimingStrategy('custom_config.json')
results = strategy.run_strategy()
```

### 单因子分析

```python
# 分析估值面因子
valuation_results = strategy.run_factor_analysis('valuation')

# 分析基本面因子
fundamental_results = strategy.run_factor_analysis('fundamental')

# 分析资金面因子
capital_results = strategy.run_factor_analysis('capital')

# 分析技术面因子
technical_results = strategy.run_factor_analysis('technical')
```

## 因子说明

### 估值面因子

1. **席勒ERP因子**
   - 使用过去6年通胀调整后的平均盈利计算席勒PE
   - 席勒ERP = 1/席勒PE - 10年期国债到期收益率

2. **PB/PS因子**
   - PB/PS × (-1)
   - 1.5倍MAD截尾后标准化到±1之间

3. **AIAE因子**
   - AIAE指标 = 中证全指总流通市值/(中证全指总流通市值+实体总债务)
   - AIAE × (-1)并计算过去3年z-score

### 基本面因子

1. **货币方向因子**: 基于央行政策工具利率与短端市场利率的变化方向
2. **货币强度因子**: 基于"利率走廊"概念的偏离度分析
3. **信用方向因子**: 中长期贷款增量的同比变化
4. **增长方向因子**: 基于PMI数据的增长方向判断
5. **增长强度因子**: PMI预期差的综合分析
6. **通胀方向因子**: CPI和PPI的综合通胀方向
7. **通胀强度因子**: 通胀预期差的分析

### 资金面因子

1. **两融增量因子**: 融资余额-融券余额的趋势分析
2. **成交额趋势因子**: 对数成交额的均线距离分析

### 技术面因子

1. **BIAS因子**: 价格与移动平均的百分比偏离度
2. **AROON因子**: 阿隆指标
3. **ADX因子**: 平均趋向指数
4. **UO因子**: 终极振荡器
5. **ATR因子**: 平均真实波幅
6. **UDVD因子**: 上下成交量差
7. **新高新低数因子**: 成分股新高新低统计

## 策略逻辑

1. **因子计算**: 计算四大类因子的得分
2. **综合评分**: 根据权重计算综合得分
3. **信号生成**: 基于综合得分生成交易信号
4. **仓位管理**: 持有权益的仓位 = 0.5 + 指标分数 × 0.5
5. **周度调仓**: 使用每周最后一个交易日信号进行调仓

## 输出结果

### 文件输出

- `factor_scores.csv`: 因子得分数据
- `signals.csv`: 交易信号数据
- `backtest_results.csv`: 回测结果数据
- `performance_report.csv`: 业绩报告

### 图表输出

- `nav_comparison.png`: 净值对比图
- `drawdown.png`: 回撤分析图
- `monthly_returns.png`: 月度收益率图
- `factor_scores.png`: 因子得分图

### 业绩指标

- 总收益率
- 年化收益率
- 年化波动率
- 夏普比率
- 最大回撤
- 胜率
- 盈亏比
- 交易成本占比

## 示例代码

详细的使用示例请参考 `example.py` 文件，包含：

1. 基本策略运行
2. 自定义配置
3. 单因子分析
4. 信号有效性分析
5. 参数优化示例

## 注意事项

1. 需要有效的Wind数据库连接
2. 确保安装了所有依赖包，特别是talib
3. 首次运行可能需要较长时间下载数据
4. 建议在运行前检查数据库连接和数据完整性

## 技术支持

如有问题请联系财通金工团队。
