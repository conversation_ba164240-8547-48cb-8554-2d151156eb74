中证800择时策略

思路整理：把影响短期市场表现的指标分为以下三个部分，分别是宏观、中观、微观，每个部分下都会设置不同数量的因子，其中包括连续型的也包括离散型的，每个因子通过一定的逻辑计算得到一个得分，每个大类内部进行等权组合然后每个大类之间加权得到最终的综合因子进行加权，最终得到一个综合得分，根据综合得分进行择时。使用每周最后一个交易日信号，用其后一个交易日的收盘价进行买卖，持有权益的仓位=0.5+指标分数×0.5，剩下仓位配置货币基金指数。策略基准为50%中证800全收益指数与50%货币基金指数，最终呈现策略和基准的净值曲线可视化图像以及年化收益率、波动率、回撤、夏普比率等指标。

具体细节：

因子处理：

宏观（经济）：
经济表现：
- 生产因子：基于中采制造业PMI、中采非制造业PMI、财新制造业PMI构建生产因子，分别计算三个PMI脉冲=PMI->计算过去十二月均值->计算同比，若当前脉冲相较于三个月之前上升，则脉冲方向为1，反之为-1。计算三个PMI脉冲方向的均值，若均值>0，则增长方向因子为1分，若均值<0，则为-1分。
- 消费因子：0.5×CPI同比平滑值+0.5×PPI同比原始值，通胀方向因子相较于三个月之前降低，则说明是通胀下行环境，此时看多，分数为1，反之则说明是通胀上行环境，此时看空，分数为-1。
- 投资因子：中长期贷款当月值->计算过去十二个月增量->计算同比,信用方向因子相比于三个月之前上升则看多，分数为1。反之则看空，分数为-1。或：固定资产投资额->计算过去十二月均值->计算同比，若当前脉冲相较于三个月之前上升，则看多，分数为1。反之则看空，分数为-1。
- 货币因子：央行使用的货币政策工具利率（DR007）与短端市场利率，并计算其相较于90天前的变化方向，利率下降时记为1分，上涨记为-1分，不变记为0分，平均分数即为货币方向因子。若货币方向因子>0，则此时判断货币政策宽松，指标分数为1，否则为-1。
所需要的数据在数据库中ShiborPrices表的b_info_rate字段，CBondRepo表的b_tender_interestrate字段

资产表现：
- 汇率因子：汇率上行利好股市，计算过去三个月人民币对美元的汇率变化，若汇率变化>0，则说明人民币贬值，此时看空，分数为-1，若汇率变化<0，则说明人民币升值，此时看多，分数为1。
- 国债因子：国债收益率下行利好股市，计算过去三个月国债到期收益率变化，若国债收益率变化>0，则说明国债收益率上升，此时看空，分数为-1，若国债收益率变化<0，则说明国债收益率下降，此时看多，分数为1。
- 信用因子：使用3年期 AA 中短期票据收益率与3年期国开债收益率的差来刻画信用利差的变化，若信用利差变化>0，则说明信用利差上升，此时看空，分数为-1，若信用利差变化<0，则说明信用利差下降，此时看多，分数为1。

中观（市场）：
资金面：
- 两融增量：计算融资余额-融券余额，并计算其过去120日均增量与过去240日均增量。若120日均增量>过去240日均增量，则说明当前杠杆资金上行，此时看多，分数为1。反之看空，分数为-1。
所需要的数据在数据库中AShareMarginTradeSum表中的s_marsum_tradingbalance和s_marsum_seclendingbalance中
- 成交额趋势：对数成交额，并计算其均线距离=ma120/ma240-1，均线距离的max(10)=max(30)=max(60)时看多，分数为1，当均线距离的min(10)=min(30)=min(60)时看空，分数为-1。
- 市场资金净流入：计算过去120日均资金净流入与过去240日均资金净流入，若120日均资金净流入>240日均资金净流入，则说明当前市场资金净流入上行，此时看多，分数为1。反之看空，分数为-1。
所需要的数据在数据库中AShareMoneyFlow表中的s_moneyflow_netamount字段
- 主力资金流向：L2净流入，计算过去120日均L2净流入与过去240日均L2净流入，若120日均L2净流入>240日均L2净流入，则说明当前市场资金净流入上行，此时看多，分数为1。反之看空，分数为-1。
- 高收益日资金流：专注于涨幅靠前的交易日的资金净流入幅度。

市场情绪：
- 恐慌指数VIX：VIX指数越高，表明市场恐慌情绪越浓，市场波动预期越大，通常预示着市场可能面临较大的不确定性，是市场下跌的先行指标。计算过去三个月VIX指数变化，若VIX指数变化>0，则说明VIX指数上升，此时看空，分数为-1，若VIX指数变化<0，则说明VIX指数下降，此时看多，分数为1。
- 认沽认购比率（PCR）：PCR越高，表明市场看跌情绪越浓，市场下跌预期越大，通常预示着市场可能面临较大的不确定性，是市场下跌的先行指标。计算过去三个月PCR变化，若PCR变化>0，则说明PCR上升，此时看空，分数为-1，若PCR变化<0，则说明PCR下降，此时看多，分数为1。

微观（标的）
基本面
- 估值-席勒ERP：使用过去6年通胀调整后的平均盈利计算席勒PE，通胀调整使用CPI与PPI环比均值，席勒ERP=1/席勒PE-10年期国债到期收益率，席勒ERP过去3年zscore，1.5倍标准差截尾后标准化到±1之间得到分数
所需要的市值、盈利数据在数据库中AShareEODDerivativeIndicator表中的s_val_mv、oper_rev_ttm字段
- 估值-PB/PS：PB/PS×(-1)并计算过去3年zscore，1.5倍标准差截尾后标准化到±1之间得到分数
所需要的PB、PS数据在数据库中AShareEODDerivativeIndicator表中的s_val_pb_new，s_val_ps_ttm字段
- 动量-过去3个月6个月12个月收益率，计算过去3个月6个月12个月收益率平均值，并计算过去3年zscore，1.5倍标准差截尾后标准化到±1之间得到分数。
- 盈利-ROE：∑(成份股.归属母公司股东的净利润*2)/∑(期初归属母公司股东的权益+期末归属母公司股东的权益)，ROE过去3年zscore，1.5倍标准差截尾后标准化到±1之间得到分数
所需要的ROE数据在数据库中AShareEODDerivativeIndicator表中的s_fa_roe字段
- 流动性-过去3个月6个月12个月换手率，计算过去3个月6个月12个月换手率平均值，并计算过去3年zscore，1.5倍标准差截尾后标准化到±1之间得到分数。


技术面：
- BIAS：价格与移动平均的百分比偏离度，BIAS大于5时产生买入信号即分数为1，BIAS小于-5时产生卖出信号即分数为-1
- AROON：AROONUp = (N-N 日最高价距当前天数)/N×100 
AROONDown = (N-N 日最低价距当前天数)/N×100 
AROON = AROONUp - AROONDown，当AROONUP大于70且AROON大于0时产生买入信号，当AROONDown大于70且AROON小于0时产生卖出信号
- ADX：可以直接使用talib库中的函数，当DI+大于DI-时产生买入信号，当DI-大于DI+时产生卖出信号
- UO：可以直接使用talib库中的函数，当UO大于70时产生买入信号，当UO小于50时产生卖出信号
- ATR：可以直接使用talib库中的函数，当股价大于上轨时产生买入信号，当股价小于下轨时产生卖出信号
- UDVD：VOLUP = (HIGH-OPEN)/OPEN 
VOLDOWN = (OPEN-LOW)/OPEN 
UDVD = SMA(VOLUP-VOLDOWN, N)，当UDVD大于0时产生买入信号，当UDVD小于0时产生卖出信号
- 新高新低数：计算中证800指数成分股为过去一年新高的数量，以及过去一年新低的数量，并计算差值=新高数-新低数，对差值计算ma20平滑。若平滑后的差值<0，则说明近期新低成分股较多，指数有见底的预期，此时发出看多信号，分数为1。反之若平滑后的差值>0，则指数有不久后见顶的可能，此时发出看空信号，分数为-1。

所需要的量价数据在AShareEODPrices 表中，AShareCalendar 中有交易日信息，指数行情数据在aindexeodprices表中

回测框架：

该回测框架需要适用于对单个指标信号进行回测，也需要适用于对综合信号进行回测，周度调仓，使用每周最后一个交易日信号，用其后一个交易日的收盘价进行买卖，持有权益的仓位=0.5+指标分数×0.5，剩下仓位配置货币基金指数。策略基准为50%中证800全收益指数与50%货币基金指数

结果呈现：

回测结果的可视化呈现，需要画出策略与基准的净值曲线对比图。以及计算净值曲线，年化收益率、波动率、回撤、夏普比率等指标进行输出。可以保留回测结果和净值曲线，方便后续进行对比分析。还需要计算每个信号产生后（综合分数大于0时）n日的平均收益率，用以比较信号本身的效果。

保留接口：

在设计因子计算以及回测框架时尽量保留尽可能的可以调整的参数接口，参数和代码可以分开，参数可以单独用一个字典或类来传。

在根目录下有一个择时策略的notebook，其中的数据库读取方式可供参考，其他的文件不需要考虑。生成的代码可以保存在一个code文件夹内。