"""
回测框架
支持单因子和综合因子回测，包括周度调仓逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, database_manager):
        """
        初始化回测引擎
        
        Args:
            database_manager: 数据库管理器实例
        """
        self.db = database_manager
        self.initial_capital = 1000000  # 初始资金100万
        self.commission_rate = 0.0003   # 手续费率0.03%
        self.money_fund_return = 0.02   # 货币基金年化收益率2%
    
    def get_benchmark_data(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        获取基准数据
        
        策略基准为50%中证800全收益指数与50%货币基金指数
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 基准数据
        """
        # 获取指数价格数据
        index_data = self.db.get_index_price_data(index_code, start_date, end_date)
        
        if index_data.empty:
            return pd.DataFrame()
        
        data = index_data.copy()
        data = data.sort_values('date').reset_index(drop=True)
        
        # 计算指数收益率
        data['index_return'] = data['pct_change']
        
        # 计算货币基金日收益率（年化2%）
        data['money_fund_daily_return'] = (self.money_fund_return + 1) ** (1/252) - 1
        
        # 计算基准收益率（50%指数 + 50%货币基金）
        data['benchmark_return'] = (
            0.5 * data['index_return'] + 
            0.5 * data['money_fund_daily_return']
        )
        
        # 计算基准净值
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()
        
        return data[['date', 'close', 'benchmark_return', 'benchmark_nav']]
    
    def run_backtest(self, signals_df, index_code='000906.SH', mode='comprehensive'):
        """
        运行回测
        
        Args:
            signals_df: 包含交易信号的DataFrame
            index_code: 指数代码
            mode: 回测模式，'comprehensive'为综合因子，'single'为单因子
            
        Returns:
            pandas.DataFrame: 回测结果
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        # 获取价格数据
        start_date = signals_df['date'].min().strftime('%Y%m%d')
        end_date = signals_df['date'].max().strftime('%Y%m%d')
        price_data = self.db.get_index_price_data(index_code, start_date, end_date)
        
        if price_data.empty:
            return pd.DataFrame()
        
        # 合并信号和价格数据
        signals_df['date'] = pd.to_datetime(signals_df['date'])
        price_data['date'] = pd.to_datetime(price_data['date'])
        
        backtest_data = price_data.merge(signals_df, on=['date', 'code'], how='left')
        backtest_data = backtest_data.sort_values('date').reset_index(drop=True)
        
        # 前向填充信号
        signal_cols = ['equity_weight', 'money_fund_weight']
        for col in signal_cols:
            if col in backtest_data.columns:
                backtest_data[col] = backtest_data[col].fillna(method='ffill')
                # 使用前一个交易日的信号进行交易
                backtest_data[col] = backtest_data[col].shift(1)

        # 初始化回测变量
        backtest_data['portfolio_value'] = self.initial_capital
        backtest_data['equity_value'] = 0
        backtest_data['money_fund_value'] = 0
        backtest_data['daily_return'] = 0
        backtest_data['cumulative_return'] = 0
        backtest_data['transaction_cost'] = 0

        # 初始权重
        if 'equity_weight' not in backtest_data.columns:
            backtest_data['equity_weight'] = 0.5
            backtest_data['money_fund_weight'] = 0.5
        
        backtest_data['equity_weight'] = backtest_data['equity_weight'].fillna(0.5)
        backtest_data['money_fund_weight'] = backtest_data['money_fund_weight'].fillna(0.5)
        
        # 计算日收益率
        backtest_data['index_return'] = backtest_data['pct_change']
        backtest_data['money_fund_daily_return'] = (self.money_fund_return + 1) ** (1/252) - 1
        
        # 逐日计算组合价值
        for i in range(len(backtest_data)):
            if i == 0:
                # 第一天
                backtest_data.loc[i, 'equity_value'] = (
                    self.initial_capital * backtest_data.loc[i, 'equity_weight']
                )
                backtest_data.loc[i, 'money_fund_value'] = (
                    self.initial_capital * backtest_data.loc[i, 'money_fund_weight']
                )
                backtest_data.loc[i, 'portfolio_value'] = self.initial_capital
                backtest_data.loc[i, 'daily_return'] = 0
                backtest_data.loc[i, 'cumulative_return'] = 0
            else:
                # 计算前一日的权重
                prev_equity_weight = backtest_data.loc[i-1, 'equity_weight']
                prev_money_weight = backtest_data.loc[i-1, 'money_fund_weight']
                
                # 当前权重
                curr_equity_weight = backtest_data.loc[i, 'equity_weight']
                curr_money_weight = backtest_data.loc[i, 'money_fund_weight']
                
                # 前一日组合价值
                prev_portfolio_value = backtest_data.loc[i-1, 'portfolio_value']
                
                # 计算当日收益（基于前一日权重）
                if not pd.isna(backtest_data.loc[i, 'index_return']):
                    equity_return = backtest_data.loc[i, 'index_return']
                else:
                    equity_return = 0
                
                money_return = backtest_data.loc[i, 'money_fund_daily_return']
                
                # 组合日收益率
                portfolio_return = (
                    prev_equity_weight * equity_return + 
                    prev_money_weight * money_return
                )
                
                # 计算交易成本（权重变化时产生）
                weight_change = abs(curr_equity_weight - prev_equity_weight)
                transaction_cost = prev_portfolio_value * weight_change * self.commission_rate
                
                # 更新组合价值
                portfolio_value = prev_portfolio_value * (1 + portfolio_return) - transaction_cost
                
                backtest_data.loc[i, 'portfolio_value'] = portfolio_value
                backtest_data.loc[i, 'daily_return'] = portfolio_return
                backtest_data.loc[i, 'transaction_cost'] = transaction_cost
                backtest_data.loc[i, 'cumulative_return'] = (
                    portfolio_value / self.initial_capital - 1
                )
                
                # 更新各部分价值
                backtest_data.loc[i, 'equity_value'] = portfolio_value * curr_equity_weight
                backtest_data.loc[i, 'money_fund_value'] = portfolio_value * curr_money_weight
        
        # 计算净值
        backtest_data['nav'] = backtest_data['portfolio_value'] / self.initial_capital
        
        return backtest_data
    
    def run_weekly_backtest(self, signals_df, index_code='000906.SH'):
        """
        运行周度调仓回测
        
        使用每周最后一个交易日信号，用其后一个交易日的收盘价进行买卖
        
        Args:
            signals_df: 包含交易信号的DataFrame
            index_code: 指数代码
            
        Returns:
            pandas.DataFrame: 周度回测结果
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        # 获取周度信号
        from .factor_scoring import FactorScoring
        factor_scoring = FactorScoring(self.db)
        weekly_signals = factor_scoring.get_weekly_signals(signals_df)
        
        # 运行回测
        return self.run_backtest(weekly_signals, index_code)
    
    def calculate_signal_effectiveness(self, signals_df, index_code='000906.SH', 
                                    forward_days=[1, 3, 5, 10, 20, 30, 60, 120]):
        """
        计算信号有效性
        
        计算每个信号产生后n日的平均收益率，用以比较信号本身的效果
        
        Args:
            signals_df: 包含交易信号的DataFrame
            index_code: 指数代码
            forward_days: 前瞻天数列表
            
        Returns:
            pandas.DataFrame: 信号有效性分析结果
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        # 获取价格数据
        start_date = signals_df['date'].min().strftime('%Y%m%d')
        end_date = (signals_df['date'].max() + timedelta(days=150)).strftime('%Y%m%d')
        price_data = self.db.get_index_price_data(index_code, start_date, end_date)
        
        if price_data.empty:
            return pd.DataFrame()
        
        # 合并数据
        signals_df['date'] = pd.to_datetime(signals_df['date'])
        price_data['date'] = pd.to_datetime(price_data['date'])
        
        merged_data = signals_df.merge(price_data, on=['date', 'code'], how='left')
        merged_data = merged_data.sort_values('date').reset_index(drop=True)
        
        # 计算前瞻收益率
        for days in forward_days:
            merged_data[f'forward_return_{days}d'] = (
                merged_data['close'].shift(-days) / merged_data['close'] - 1
            )
        
        # 分析信号有效性
        results = []
        
        # 分析综合分数大于0的信号
        positive_signals = merged_data[merged_data['comprehensive_score'] > 0]
        
        if not positive_signals.empty:
            for days in forward_days:
                avg_return = positive_signals[f'forward_return_{days}d'].mean()
                win_rate = (positive_signals[f'forward_return_{days}d'] > 0).mean()
                
                results.append({
                    'signal_type': 'positive_score',
                    'forward_days': days,
                    'avg_return': avg_return,
                    'win_rate': win_rate,
                    'signal_count': len(positive_signals)
                })
        
        # 分析综合分数小于0的信号
        negative_signals = merged_data[merged_data['comprehensive_score'] < 0]
        
        if not negative_signals.empty:
            for days in forward_days:
                avg_return = negative_signals[f'forward_return_{days}d'].mean()
                win_rate = (negative_signals[f'forward_return_{days}d'] > 0).mean()
                
                results.append({
                    'signal_type': 'negative_score',
                    'forward_days': days,
                    'avg_return': avg_return,
                    'win_rate': win_rate,
                    'signal_count': len(negative_signals)
                })
        
        return pd.DataFrame(results)
    
    def get_backtest_summary(self, backtest_results):
        """
        获取回测结果汇总
        
        Args:
            backtest_results: 回测结果DataFrame
            
        Returns:
            dict: 回测汇总统计
        """
        if backtest_results.empty:
            return {}
        
        # 计算基本统计指标
        total_return = backtest_results['cumulative_return'].iloc[-1]
        daily_returns = backtest_results['daily_return'].dropna()
        
        # 年化收益率
        trading_days = len(daily_returns)
        annualized_return = (1 + total_return) ** (252 / trading_days) - 1
        
        # 年化波动率
        annualized_volatility = daily_returns.std() * np.sqrt(252)
        
        # 夏普比率
        excess_returns = daily_returns - ((1+self.money_fund_return)**(1/252)-1)
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        
        # 最大回撤
        nav_series = backtest_results['nav']
        rolling_max = nav_series.expanding().max()
        drawdown = (nav_series - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (daily_returns > 0).mean()
        
        # 盈亏比
        positive_returns = daily_returns[daily_returns > 0]
        negative_returns = daily_returns[daily_returns < 0]
        
        if len(negative_returns) > 0:
            profit_loss_ratio = positive_returns.mean() / abs(negative_returns.mean())
        else:
            profit_loss_ratio = np.inf
        
        # 交易成本
        total_transaction_cost = backtest_results['transaction_cost'].sum()
        transaction_cost_ratio = total_transaction_cost / self.initial_capital
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': annualized_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'total_transaction_cost': total_transaction_cost,
            'transaction_cost_ratio': transaction_cost_ratio,
            'trading_days': trading_days
        }
